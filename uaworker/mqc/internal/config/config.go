package config

import (
	"github.com/electricbubble/gadb"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/clickPilot"
)

type Config struct {
	service.ServiceConf

	Redis           redis.RedisKeyConf
	DispatcherRedis redis.RedisConf

	Discovery zrpc.RpcClientConf `json:",optional"`
	Reporter  zrpc.RpcClientConf

	UIAgentWorkerConsumer consumer.Config
	DispatcherProducer    producer.Config
	ManagerProducer       producer.Config
	ReporterProducer      producer.Config

	ADB        gadb.Config `json:",optional"`
	ClickPilot clickPilot.Config

	LocalPath string
}

func (c Config) LogConfig() logx.LogConf {
	return c.ServiceConf.Log
}

func (c Config) ListenOn() string {
	return constants.NoNeedToListen
}
