syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

import "validate/validate.proto";

import "sqlbuilder/search.proto";
import "user/user.proto";
import "common/config.proto";
import "common/enum.proto";
import "common/lark.proto";
import "common/limit.proto";
import "common/perf.proto";
import "common/stability.proto";
import "common/ui_agent.proto";
import "manager/base.proto";
import "manager/element.proto";
import "manager/extra.proto";
import "manager/project.proto";
import "manager/category.proto";
import "manager/tag.proto";
import "manager/function.proto";
import "manager/generalconfig.proto";
import "manager/accountconfig.proto";
import "manager/protobufconfig.proto";
import "manager/component.proto";
import "manager/interfacedefinition.proto";
import "manager/componentgroup.proto";
import "manager/apicase.proto";
import "manager/apisuite.proto";
import "manager/apiplan.proto";
import "manager/advancedsearch.proto";
import "manager/notify.proto";
import "manager/gitconfig.proto";
import "manager/uiplan.proto";
import "manager/review.proto";
import "manager/casepublic.proto";
import "manager/perfdata.proto";
import "manager/perfcase.proto";
import "manager/perfplan.proto";
import "manager/perf_stop_rule.proto";
import "manager/perf_lark_chat.proto";
import "manager/project_device.proto";
import "manager/stability_plan.proto";
import "manager/lark_chat.proto";
import "manager/sla_threshold.proto";
import "manager/prompt_config.proto";
import "manager/application_config.proto";
import "manager/ui_agent.proto";


//ApiExecutionService API执行服务
service ApiExecutionService {
  //GetApiExecutionData 获取API执行数据
  rpc GetApiExecutionData(GetApiExecutionDataReq) returns (ApiExecutionData);
  //GetApiExecutionDataStream 批量获取API执行数据（服务端流式）
  rpc GetApiExecutionDataStream(GetApiExecutionDataStreamReq) returns (stream ApiExecutionData);
  //GetApiExecutionDataStructure 获取API执行数据结构
  rpc GetApiExecutionDataStructure(GetApiExecutionDataStructureReq) returns (ApiExecutionData);
}

// GetApiExecutionDataReq 获取API执行数据请求消息
message GetApiExecutionDataReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  ApiExecutionDataType type = 2 [(validate.rules).enum = {in: [1, 2, 5, 9, 93, 94, 95]}]; // API执行数据类型，如：组件组、API测试用例、接口用例、UI测试计划、压力测试计划、稳定性测试计划、UI Agent组件
  string id = 3 [(validate.rules).string = {pattern: "(?:component_group|case|interface_case|ui_plan|perf_plan|stability_plan|ui_agent_component)_id:.+?"}]; // 根据上面的类型决定是哪种ID，如：组件组ID、API测试用例ID、接口用例ID、UI测试计划ID、压力测试计划ID、稳定性测试计划ID、UI Agent组件ID
  string version = 4 [(validate.rules).string = {ignore_empty: true, prefix: "version:"}]; // 根据上面的类型决定是哪种版本，如：组件组版本、API测试用例版本、接口用例版本
  oneof extra {
    UiPlanExtraData ui_plan_extra_data = 12; // UI测试计划额外数据
  }

  reserved 11;
}
// GetApiExecutionDataStreamReq 获取API执行数据流式请求消息
message GetApiExecutionDataStreamReq {
  message IdVersion {
    string id = 1 [(validate.rules).string = {pattern: "(?:component_group|case|interface_case)_id:.+?"}]; // 根据上面的类型决定是哪种ID，如：组件组ID、API测试用例ID、接口用例ID
    string version = 2 [(validate.rules).string = {ignore_empty: true, prefix: "version:"}]; // 根据上面的类型决定是哪种版本，如：组件组版本、API测试用例版本、接口用例版本
  }
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  ApiExecutionDataType type = 2 [(validate.rules).enum = {in: [1, 2, 5]}]; // API执行数据类型，如：组件组、API测试用例、接口用例
  repeated IdVersion items = 3 [(validate.rules).repeated = {min_items: 1, items: {message: {required: true}}}];
}
// GetApiExecutionDataStructure 获取API执行数据结构请求消息
message GetApiExecutionDataStructureReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  ApiExecutionDataType type = 2 [(validate.rules).enum = {in: [3, 4, 6]}]; // API执行数据类型，如：API测试集合、API测试计划、接口文档（集合）
  string id = 3 [(validate.rules).string = {pattern: "(?:suite|plan|interface_document)_id:.+?"}]; // 根据上面的类型决定是哪种ID，如：API测试集合ID、API测试计划ID、接口ID
  oneof extra {
    ApiPlanExtraData api_plan_extra_data = 11; // API测试计划额外数据
    UiPlanExtraData ui_plan_extra_data = 12; // UI测试计划额外数据
  }
}
// ApiExecutionData API执行数据
message ApiExecutionData {
  message ChildData {
    repeated ApiExecutionData child = 1;
  }
  message ErrorData {
    uint32 code = 1;    // 错误码
    string message = 2; // 错误信息
  }
  message ServiceCasesData {
    string service = 1;
    repeated string cases = 2;
  }

  string id = 1;
  ApiExecutionDataType type = 2;
  oneof data {
    ComponentGroupComponent group = 11;
    CaseComponent case = 12;
    SuiteComponent suite = 13;
    PlanComponent plan = 14;
    InterfaceCaseComponent interface_case = 15;
    InterfaceDocumentComponent interface_document = 16;
    UICaseComponent ui_case = 17;
    UISuiteComponent ui_suite = 18;
    UIPlanComponent ui_plan = 19;
    ServiceComponent service = 20;
    PerfCaseComponent perf_case = 91;
    PerfSuiteComponent perf_suite = 92;
    PerfPlanComponent perf_plan = 93;
    StabilityPlanComponent stability_plan = 94;
    UIAgentComponentComponent ui_agent_component = 95;
    UIAgentCaseComponent ui_agent_case = 96;
    UIAgentPlanComponent ui_agent_plan = 97;

    StartComponent start = 21;
    EndComponent end = 22;

    SetupComponent setup = 31;
    TeardownComponent teardown = 32;
    BusinessSingleComponent business_single = 33;
    BusinessGroupComponent business_group = 34;
    LoopComponent loop = 35;

    HttpRequestComponent http_request = 51;
    ReferenceComponent reference = 52;
    ConditionComponent condition = 53;
    WaitComponent wait = 54;
    AssertComponent assert = 55;
    PoolAccountComponent account = 56;
    DataProcessingComponent processing = 57;
    DataDrivenComponent driven = 58;
    SqlExecutionComponent sql = 59;
    HttpRequestComponent precision_testing_http_request = 60;
  }
  repeated ChildData children = 3;
  ErrorData error = 4;
  repeated ServiceCasesData service_cases_content = 5;
}


//ProjectService 项目服务
service ProjectService {
  //CreateProject 创建项目
  rpc CreateProject(CreateProjectReq) returns (CreateProjectResp);
  //RemoveProject 删除项目
  rpc RemoveProject(RemoveProjectReq) returns (RemoveProjectResp);
  //ModifyProject 编辑项目
  rpc ModifyProject(ModifyProjectReq) returns (ModifyProjectResp);
  //SearchProject 搜索项目
  rpc SearchProject(SearchProjectReq) returns (SearchProjectResp);
  //ViewProject 查看项目
  rpc ViewProject(ViewProjectReq) returns (ViewProjectResp);
  //SearchProjectUser 搜索项目用户
  rpc SearchProjectUser(SearchProjectUserReq) returns (SearchProjectUserResp);
  //ModifyProjectReviewFunction 开启、关闭用例审核功能
  rpc ModifyProjectReviewFunction(ModifyProjectReviewFunctionReq) returns (ModifyProjectReviewFunctionResp);
  //ModifyProjectCoverageFunction 开启、关闭接口用例覆盖率功能
  rpc ModifyProjectCoverageFunction(ModifyProjectCoverageFunctionReq) returns (ModifyProjectCoverageFunctionResp);
}

message CreateProjectReq {
  string name = 1 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 项目名称
  string description = 2 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 项目描述

  bool review_enabled = 11; // 是否开启用例审核功能
}
message CreateProjectResp {
  Project project = 1;
}

message RemoveProjectReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
}
message RemoveProjectResp {}

message ModifyProjectReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 项目名称
  string description = 3 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 项目描述

  bool review_enabled = 11; // 是否开启用例审核功能

  bool coverage_enabled = 21; // 是否开启接口用例覆盖率统计功能
  repeated common.LarkChat coverage_lark_chats = 22 [(validate.rules).repeated = {ignore_empty: true, max_items: 5, items: {message: {required: true}}}]; // 接口用例覆盖率飞书通知群组
}
message ModifyProjectResp {
  Project project = 1;
}

message SearchProjectReq {
  sqlbuilder.Condition condition = 1; // 查询条件
  sqlbuilder.Pagination pagination = 2; // 查询分页
  repeated sqlbuilder.SortField sort = 3 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchProjectResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated Project items = 5;
}

message ViewProjectReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
}
message ViewProjectResp {
  Project project = 1;
}

message SearchProjectUserReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  sqlbuilder.Condition condition = 2; // 查询条件
  sqlbuilder.Pagination pagination = 3; // 查询分页
  repeated sqlbuilder.SortField sort = 4 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchProjectUserResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated user.UserInfo items = 5;
}

message ModifyProjectReviewFunctionReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  bool review_enabled = 2; // 是否开启用例审核功能
}
message ModifyProjectReviewFunctionResp {
  Project project = 1;
}

message ModifyProjectCoverageFunctionReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  bool coverage_enabled = 2; // 是否开启接口用例覆盖率统计功能
  repeated common.LarkChat coverage_lark_chats = 3 [(validate.rules).repeated = {ignore_empty: true, max_items: 5, items: {message: {required: true}}}]; // 接口用例覆盖率飞书通知群组
}
message ModifyProjectCoverageFunctionResp {
  Project project = 1;
}


//CategoryService 分类服务
service CategoryService {
  //CreateCategory 创建分类
  rpc CreateCategory(CreateCategoryReq) returns (CreateCategoryResp);
  //RemoveCategory 删除分类
  rpc RemoveCategory(RemoveCategoryReq) returns (RemoveCategoryResp);
  //ModifyCategory 编辑分类
  rpc ModifyCategory(ModifyCategoryReq) returns (ModifyCategoryResp);
  //SearchCategory 搜索分类
  rpc SearchCategory(SearchCategoryReq) returns (SearchCategoryResp);
  //MoveCategoryTree 移动分类树
  rpc MoveCategoryTree(MoveCategoryTreeReq) returns (MoveCategoryTreeResp);
  //GetCategoryTree 获取分类树
  rpc GetCategoryTree(GetCategoryTreeReq) returns (GetCategoryTreeResp);
}

message CreateCategoryReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string type = 2 [(validate.rules).string = {in: ["INTERFACE_DOCUMENT", "INTERFACE_SCHEMA", "COMPONENT_GROUP", "API_CASE", "API_SUITE", "API_PLAN", "UI_PLAN", "PERF_CASE", "PERF_PLAN", "STABILITY_PLAN", "UI_AGENT_COMPONENT"]}]; // 分类树类型
  string name = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 分类名称
  string description = 4 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 分类描述
  string parent_id = 5; // 父分类ID
  int64 index = 6; // 分类所在层的序号
}
message CreateCategoryResp {
  Category category = 1;
}

message RemoveCategoryReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string type = 2 [(validate.rules).string = {in: ["INTERFACE_DOCUMENT", "INTERFACE_SCHEMA", "COMPONENT_GROUP", "API_CASE", "API_SUITE", "API_PLAN", "UI_PLAN", "PERF_CASE", "PERF_PLAN", "STABILITY_PLAN", "UI_AGENT_COMPONENT"]}]; // 分类树类型
  string category_id = 3 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
}
message RemoveCategoryResp {}

message ModifyCategoryReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string type = 2 [(validate.rules).string = {in: ["INTERFACE_DOCUMENT", "INTERFACE_SCHEMA", "COMPONENT_GROUP", "API_CASE", "API_SUITE", "API_PLAN", "UI_PLAN", "PERF_CASE", "PERF_PLAN", "STABILITY_PLAN", "UI_AGENT_COMPONENT"]}]; // 分类树类型
  string category_id = 3 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  string name = 4 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 分类名称
  string description = 5 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 分类描述
}
message ModifyCategoryResp {
  Category category = 1;
}

message SearchCategoryReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string type = 2 [(validate.rules).string = {in: ["INTERFACE_DOCUMENT", "INTERFACE_SCHEMA", "COMPONENT_GROUP", "API_CASE", "API_SUITE", "API_PLAN", "UI_PLAN", "PERF_CASE", "PERF_PLAN", "STABILITY_PLAN", "UI_AGENT_COMPONENT"]}]; // 分类树类型
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchCategoryResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated Category items = 5;
}

message MoveCategoryTreeReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string type = 2 [(validate.rules).string = {in: ["INTERFACE_DOCUMENT", "INTERFACE_SCHEMA", "COMPONENT_GROUP", "API_CASE", "API_SUITE", "API_PLAN", "UI_PLAN", "PERF_CASE", "PERF_PLAN", "STABILITY_PLAN", "UI_AGENT_COMPONENT"]}]; // 分类树类型
  string move_type = 3 [(validate.rules).string = {in: ["BEFORE", "AFTER", "INNER"]}]; // 移到类型
  string source_id = 4 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 源分类ID
  string target_id = 5 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 目标分类ID
  string sibling_id = 6 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^category_id:.+?)"}]; // 兄弟分类ID
  bool being_modified = 7; // 是否在编辑流程中
}
message MoveCategoryTreeResp {}

message GetCategoryTreeReq {
  string project_id = 1 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string type = 2 [(validate.rules).string = {in: ["INTERFACE_DOCUMENT", "INTERFACE_SCHEMA", "COMPONENT_GROUP", "API_CASE", "API_SUITE", "API_PLAN", "UI_PLAN", "PERF_CASE", "PERF_PLAN", "STABILITY_PLAN", "UI_AGENT_COMPONENT"]}]; // 分类树类型
  string category_id = 3 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^category_id:.+?)"}]; // 分类ID
  uint32 depth = 4 [(validate.rules).uint32.gte = 0]; // 获取分类树的最大深度
  bool only_directory = 5; // 只获取`DIRECTORY`类型的节点
  bool include_self = 6; // 是否包括当前节点
}
message GetCategoryTreeResp {
  repeated Category category_tree = 1;
}


//TagService 标签服务
service TagService {
  //CreateTag 创建标签
  rpc CreateTag(CreateTagReq) returns (CreateTagResp);
  //RemoveTag 删除标签
  rpc RemoveTag(RemoveTagReq) returns (RemoveTagResp);
  //ModifyTag 编辑标签
  rpc ModifyTag(ModifyTagReq) returns (ModifyTagResp);
  //SearchTag 查询标签
  rpc SearchTag(SearchTagReq) returns (SearchTagResp);
  //ViewTag   查看标签
  rpc ViewTag(ViewTagReq) returns (ViewTagResp);
}

message CreateTagReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string type = 2 [(validate.rules).string = {in: ["COMPONENT_GROUP", "CASE", "SUITE", "PLAN", "INTERFACE_DOCUMENT", "UI_AGENT"]}]; // 标签类型
  string name = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 项目名称
  string description = 4 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 项目描述
}
message CreateTagResp {
  Tag tag = 1;
}

message RemoveTagReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string tag_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^tag_id:.+?)"}}}]; // 标签ID列表
}
message RemoveTagResp {}

message ModifyTagReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string type = 2 [(validate.rules).string = {in: ["COMPONENT_GROUP", "CASE", "SUITE", "PLAN", "INTERFACE_DOCUMENT", "UI_AGENT"]}]; // 标签类型
  string tag_id = 3 [(validate.rules).string = {pattern: "(?:^tag_id:.+?)"}]; // 标签ID
  string name = 4 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 项目名称
  string description = 5 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 项目描述
  int64 status = 6 [(validate.rules).int64 = {ignore_empty: true, in: [0, 1, 2]}]; // 标签状态
}
message ModifyTagResp {
  Tag tag = 1;
}

message SearchTagReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string type = 2 [(validate.rules).string = {in: ["COMPONENT_GROUP", "CASE", "SUITE", "PLAN", "INTERFACE_DOCUMENT", "UI_AGENT"]}]; // 标签分类
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchTagResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated Tag items = 5;
}

message ViewTagReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string tag_id = 2 [(validate.rules).string = {pattern: "(?:^tag_id:.+?)"}]; // 标签ID
}
message ViewTagResp {
  Tag tag = 1;
}


//DataProcessingFunctionService 数据处理函数服务
service DataProcessingFunctionService {
  //CreateDataProcessingFunction 创建数据处理函数
  rpc CreateDataProcessingFunction(CreateOrModifyDataProcessingFunctionReq) returns (CreateOrModifyDataProcessingFunctionResp);
  //RemoveDataProcessingFunction 删除数据处理函数
  rpc RemoveDataProcessingFunction(RemoveDataProcessingFunctionReq) returns (RemoveDataProcessingFunctionResp);
  //ModifyDataProcessingFunction 编辑数据处理函数
  rpc ModifyDataProcessingFunction(CreateOrModifyDataProcessingFunctionReq) returns (CreateOrModifyDataProcessingFunctionResp);
  //SearchDataProcessingFunction 搜索数据处理函数
  rpc SearchDataProcessingFunction(SearchDataProcessingFunctionReq) returns (SearchDataProcessingFunctionResp);
  //ViewDataProcessingFunction 查看数据处理函数
  rpc ViewDataProcessingFunction(ViewDataProcessingFunctionReq) returns (ViewDataProcessingFunctionResp);
}

message CreateOrModifyDataProcessingFunctionReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 函数名称
  string category = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 函数分类
  string description = 4 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 函数描述
  CodeLanguage language = 5 [(validate.rules).enum.defined_only = true]; // 编程语言
  string content = 6; // 函数内容
  repeated Parameter parameters = 7 [(validate.rules).repeated = {min_items: 0, items: {message: {required: true}}}]; // 参数列表
  repeated Return returns = 8 [(validate.rules).repeated = {min_items: 0, items: {message: {required: true}}}]; // 返回值列表
  string example = 9; // 函数使用例子
}
message CreateOrModifyDataProcessingFunctionResp {
  DataProcessingFunction function = 1;
}

message RemoveDataProcessingFunctionReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string names = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {min_len: 1, max_len: 64}}}]; // 自定义函数名称列表
}
message RemoveDataProcessingFunctionResp {}

message SearchDataProcessingFunctionReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  sqlbuilder.Condition condition = 2; // 查询条件
  sqlbuilder.Pagination pagination = 3; // 查询分页
  repeated sqlbuilder.SortField sort = 4 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchDataProcessingFunctionResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated DataProcessingFunction items = 5;
}

message ViewDataProcessingFunctionReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 函数名称
  FunctionType type = 3 [(validate.rules).enum.defined_only = true]; // 函数类型
}
message ViewDataProcessingFunctionResp {
  DataProcessingFunction function = 1;
}


//GeneralConfigurationService 通用配置服务
service GeneralConfigurationService {
  //CreateGeneralConfiguration 创建通用配置
  rpc CreateGeneralConfiguration(CreateGeneralConfigurationReq) returns (CreateGeneralConfigurationResp);
  //RemoveGeneralConfiguration 删除通用配置
  rpc RemoveGeneralConfiguration(RemoveGeneralConfigurationReq) returns (RemoveGeneralConfigurationResp);
  //ModifyGeneralConfiguration 编辑通用配置
  rpc ModifyGeneralConfiguration(ModifyGeneralConfigurationReq) returns (ModifyGeneralConfigurationResp);
  //SearchGeneralConfiguration 搜索通用配置
  rpc SearchGeneralConfiguration(SearchGeneralConfigurationReq) returns (SearchGeneralConfigurationResp);
  //ViewGeneralConfiguration 查看通用配置
  rpc ViewGeneralConfiguration(ViewGeneralConfigurationReq) returns (ViewGeneralConfigurationResp);
}

message CreateGeneralConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string type = 2 [(validate.rules).string = {in: ["API", "PERF"]}]; // 类型
  string name = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 通用配置名称
  string description = 4 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 通用配置描述
  string base_url = 5 [(validate.rules).string = {ignore_empty: true, max_len: 128, uri_ref: true}]; // 通用配置中的HTTP请求基础URL
  bool verify = 6; // 是否开启SSL
  repeated common.GeneralConfigVar variables = 7 [(validate.rules).repeated = {ignore_empty: true, min_items: 0, items: {message: {required: true}}}]; // 通用配置中的变量列表
}
message CreateGeneralConfigurationResp {
  GeneralConfiguration configuration = 1;
}

message RemoveGeneralConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string config_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^general_config_id:.+?)"}}}]; // 通用配置ID列表
}
message RemoveGeneralConfigurationResp {}

message ModifyGeneralConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string config_id = 2 [(validate.rules).string = {pattern: "(?:^general_config_id:.+?)"}]; // 通用配置ID
  string name = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 通用配置名称
  string description = 4 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 通用配置描述
  string base_url = 5 [(validate.rules).string = {ignore_empty: true, max_len: 128, uri_ref: true}]; // 通用配置中的HTTP请求基础URL
  bool verify = 6; // 是否开启SSL
  repeated common.GeneralConfigVar variables = 7 [(validate.rules).repeated = {ignore_empty: true, min_items: 0, items: {message: {required: true}}}]; // 通用配置中的变量列表
}
message ModifyGeneralConfigurationResp {
  GeneralConfiguration configuration = 1;
}

message SearchGeneralConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  sqlbuilder.Condition condition = 2; // 查询条件
  sqlbuilder.Pagination pagination = 3; // 查询分页
  repeated sqlbuilder.SortField sort = 4 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchGeneralConfigurationResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated GeneralConfiguration items = 5;
}

message ViewGeneralConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string config_id = 2 [(validate.rules).string = {pattern: "(?:^general_config_id:.+?)"}]; // 通用配置ID
}
message ViewGeneralConfigurationResp {
  GeneralConfiguration configuration = 1;
}


//AccountConfigurationService 池账号配置服务
service AccountConfigurationService {
  //CreateAccountConfiguration 创建池账号配置
  rpc CreateAccountConfiguration(CreateAccountConfigurationReq) returns (CreateAccountConfigurationResp);
  //RemoveAccountConfiguration 删除池账号配置
  rpc RemoveAccountConfiguration(RemoveAccountConfigurationReq) returns (RemoveAccountConfigurationResp);
  //ModifyAccountConfiguration 编辑池账号配置
  rpc ModifyAccountConfiguration(ModifyAccountConfigurationReq) returns (ModifyAccountConfigurationResp);
  //SearchAccountConfiguration 搜索池账号配置
  rpc SearchAccountConfiguration(SearchAccountConfigurationReq) returns (SearchAccountConfigurationResp);
  //ViewAccountConfiguration 查看池账号配置
  rpc ViewAccountConfiguration(ViewAccountConfigurationReq) returns (ViewAccountConfigurationResp);
}

message CreateAccountConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 池账号配置名称"`
  string description = 3 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 池账号配置描述"`
  int64  product_type = 4; // 产品类型
  string product_name = 5 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 产品类型名称
  string pool_env_table = 6 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 账号池环境数据库表名
  string pool_env_name = 7 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 账号池环境名称
}
message CreateAccountConfigurationResp {
  AccountConfiguration configuration = 1;
}

message RemoveAccountConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string config_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^account_config_id:.+?)"}}}]; // 通用配置ID列表
}
message RemoveAccountConfigurationResp {}

message ModifyAccountConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string config_id = 2 [(validate.rules).string = {pattern: "(?:^account_config_id:.+?)"}]; // 账号池配置ID
  string name = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 池账号配置名称"`
  string description = 4 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 池账号配置描述"`
  int64  product_type = 5; // 产品类型
  string product_name = 6 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 产品类型名称
  string pool_env_table = 7 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 账号池环境数据库表名
  string pool_env_name = 8 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 账号池环境名称
}
message ModifyAccountConfigurationResp {
  AccountConfiguration configuration = 1;
}

message SearchAccountConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  sqlbuilder.Condition condition = 2; // 查询条件
  sqlbuilder.Pagination pagination = 3; // 查询分页
  repeated sqlbuilder.SortField sort = 4 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchAccountConfigurationResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated AccountConfiguration items = 5;
}

message ViewAccountConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string config_id = 2 [(validate.rules).string = {pattern: "(?:^account_config_id:.+?)"}]; // 账号池配置ID
}
message ViewAccountConfigurationResp {
  AccountConfiguration configuration = 1;
}


//InterfaceDefinitionService 接口定义服务
service InterfaceDefinitionService {
  //LocalImportInterfaceDefinition 本地导入接口定义
  rpc LocalImportInterfaceDefinition(LocalImportInterfaceDefinitionReq) returns (LocalImportInterfaceDefinitionResp);

  //CreateInterfaceDocument 创建接口文档
  rpc CreateInterfaceDocument(CreateInterfaceDocumentReq) returns (CreateInterfaceDocumentResp);
  //RemoveInterfaceDocument 删除接口文档
  rpc RemoveInterfaceDocument(RemoveInterfaceDocumentReq) returns (RemoveInterfaceDocumentResp);
  //ModifyInterfaceDocument 编辑接口文档
  rpc ModifyInterfaceDocument(ModifyInterfaceDocumentReq) returns (ModifyInterfaceDocumentResp);
  //SearchInterfaceDocument 搜索接口文档
  rpc SearchInterfaceDocument(SearchInterfaceDocumentReq) returns (SearchInterfaceDocumentResp);
  //ViewInterfaceDocument 查看接口文档
  rpc ViewInterfaceDocument(ViewInterfaceDocumentReq) returns (ViewInterfaceDocumentResp);
  //MockInterfaceDocument 根据接口文档生成接口用例数据
  rpc MockInterfaceDocument(MockInterfaceDocumentReq) returns (MockInterfaceDocumentResp);
  //SearchInterfaceDocumentReference 搜索接口集合引用详情
  rpc SearchInterfaceDocumentReference(SearchInterfaceDocumentReferenceReq) returns (SearchInterfaceDocumentReferenceResp);
  //ModifyInterfaceDocumentReferenceState 修改接口集合所在的API计划的引用状态
  rpc ModifyInterfaceDocumentReferenceState(ModifyInterfaceDocumentReferenceStateReq) returns (ModifyInterfaceDocumentReferenceStateResp);

  //CreateInterfaceSchema 创建接口数据模型
  rpc CreateInterfaceSchema(CreateInterfaceSchemaReq) returns (CreateInterfaceSchemaResp);
  //RemoveInterfaceSchema 删除接口数据模型
  rpc RemoveInterfaceSchema(RemoveInterfaceSchemaReq) returns (RemoveInterfaceSchemaResp);
  //ModifyInterfaceSchema 编辑接口数据模型
  rpc ModifyInterfaceSchema(ModifyInterfaceSchemaReq) returns (ModifyInterfaceSchemaResp);
  //SearchInterfaceSchema 搜索接口数据模型
  rpc SearchInterfaceSchema(SearchInterfaceSchemaReq) returns (SearchInterfaceSchemaResp);
  //ViewInterfaceSchema 查看接口数据模型
  rpc ViewInterfaceSchema(ViewInterfaceSchemaReq) returns (ViewInterfaceSchemaResp);

  //CreateInterfaceConfig 创建接口配置
  rpc CreateInterfaceConfig(CreateInterfaceConfigReq) returns (CreateInterfaceConfigResp);
  //RemoveInterfaceConfig 删除接口配置
  rpc RemoveInterfaceConfig(RemoveInterfaceConfigReq) returns (RemoveInterfaceConfigResp);
  //ModifyInterfaceConfig 编辑接口配置
  rpc ModifyInterfaceConfig(ModifyInterfaceConfigReq) returns (ModifyInterfaceConfigResp);
  //SearchInterfaceConfig 搜索接口配置
  rpc SearchInterfaceConfig(SearchInterfaceConfigReq) returns (SearchInterfaceConfigResp);
  //ViewInterfaceConfig 查看接口配置
  rpc ViewInterfaceConfig(ViewInterfaceConfigReq) returns (ViewInterfaceConfigResp);

  //CreateInterfaceCase 创建接口用例
  rpc CreateInterfaceCase(CreateInterfaceCaseReq) returns (CreateInterfaceCaseResp);
  //RemoveInterfaceCase 删除接口用例
  rpc RemoveInterfaceCase(RemoveInterfaceCaseReq) returns (RemoveInterfaceCaseResp);
  //ModifyInterfaceCase 编辑接口用例
  rpc ModifyInterfaceCase(ModifyInterfaceCaseReq) returns (ModifyInterfaceCaseResp);
  //SearchInterfaceCase 搜索接口用例
  rpc SearchInterfaceCase(SearchInterfaceCaseReq) returns (SearchInterfaceCaseResp);
  //ViewInterfaceCase 查看接口用例
  rpc ViewInterfaceCase(ViewInterfaceCaseReq) returns (ViewInterfaceCaseResp);
  //MaintainInterfaceCase 维护接口用例
  rpc MaintainInterfaceCase(MaintainInterfaceCaseReq) returns (MaintainInterfaceCaseResp);
  //PublishInterfaceCase 发布接口用例
  rpc PublishInterfaceCase(PublishInterfaceCaseReq) returns (PublishInterfaceCaseResp);
  //SearchInterfaceCaseReference 搜索接口用例引用详情
  rpc SearchInterfaceCaseReference(SearchInterfaceCaseReferenceReq) returns (SearchInterfaceCaseReferenceResp);

  //UpdateInterfaceCoverageData 更新接口覆盖率数据
  rpc UpdateInterfaceCoverageData(UpdateInterfaceCoverageDataReq) returns (UpdateInterfaceCoverageDataResp);
  //GetInterfaceCoverageTeams 获取接口覆盖率相关的团队
  rpc GetInterfaceCoverageTeams(GetInterfaceCoverageTeamsReq) returns (GetInterfaceCoverageTeamsResp);
  //GetInterfaceCoverageData 获取接口覆盖率数据
  rpc GetInterfaceCoverageData(GetInterfaceCoverageDataReq) returns (GetInterfaceCoverageDataResp);
  //UpdateInterfaceDocumentTags 更新接口文档标签
  rpc UpdateInterfaceDocumentTags(UpdateInterfaceDocumentTagsReq) returns (UpdateInterfaceDocumentTagsResp);
}

message LocalImportInterfaceDefinitionReq {
  message Target {
    string git_url = 1 [(validate.rules).string = {ignore_empty: true, uri: true}]; // git地址（如需权限则需要带上`access token`）
    string branch = 2 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 分支
    string path = 3 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 本地路径
  }
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string type = 2 [(validate.rules).string = {in: ["OpenApi", "gRPC", "YApi", "TT", "TTMeta", "Recommend"]}]; // 接口类型
  Target target = 3 [(validate.rules).message = {required: true}]; // 导入目标
  repeated Target dependencies = 4 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 导入目标关联的依赖
}
message LocalImportInterfaceDefinitionResp {
  Statistic document = 1;
  Statistic schema = 2;
}

message CreateInterfaceDocumentReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  string name = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 接口名称
  string description = 4 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 接口描述
  string type = 5 [(validate.rules).string = {in: ["HTTP", "gRPC"]}]; // 接口类型
  string mode = 6 [(validate.rules).string = {in: ["builtin", "local", "remote", "manual", "plugin"]}]; // 创建方式
  //string import_type = 7 [(validate.rules).string = {in: ["OpenApi", "gRPC", "Probe", "YApi", "TT"]}]; // 导入类型
  int64 status = 8 [(validate.rules).int64.gte = 1]; // 接口状态
  int64 priority = 9 [(validate.rules).int64 = {ignore_empty: true, gte: 0}]; // 优先级
  repeated string tags = 10 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len:1, max_len: 64}}}]; // 接口标签
  string service = 11 [(validate.rules).string.max_len = 64]; // 服务名称
  string path = 12 [(validate.rules).string = {min_len: 1, max_len: 128}]; // 接口路径
  string method = 13 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 接口方法
  Document data = 14 [(validate.rules).message.required = true]; // 接口详细数据
  string maintained_by = 15 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 接口维护者
}
message CreateInterfaceDocumentResp {
  InterfaceDocument document = 1;
}

message RemoveInterfaceDocumentReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string document_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^interface_document_id:.+?)"}}}]; // 接口ID列表
}
message RemoveInterfaceDocumentResp {}

message ModifyInterfaceDocumentReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  string document_id = 3 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^interface_document_id:.+?)"}]; // 接口ID
  string name = 4 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 接口名称
  string description = 5 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 接口描述
  int64 status = 6 [(validate.rules).int64.gte = 1]; // 接口状态
  int64 priority = 7 [(validate.rules).int64 = {ignore_empty: true, gte: 0}]; // 优先级
  repeated string tags = 8 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len:1, max_len: 64}}}]; // 接口标签
  CommonState state = 9 [(validate.rules).enum = {in: [1, 2]}]; // 接口集合状态
  string service = 10 [(validate.rules).string.max_len = 64]; // 服务名称
  string path = 11 [(validate.rules).string = {min_len: 1, max_len: 128}]; // 接口路径
  string method = 12 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 接口方法
  Document data = 13 [(validate.rules).message.required = true]; // 接口详细数据
  string maintained_by = 14 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 接口维护者
}
message ModifyInterfaceDocumentResp {
  InterfaceDocument document = 1;
}

message SearchInterfaceDocumentReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchInterfaceDocumentResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated InterfaceDocument items = 5;
}

message ViewInterfaceDocumentReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string document_id = 2 [(validate.rules).string = {pattern: "(?:^interface_document_id:.+?)"}]; // 接口ID
}
message ViewInterfaceDocumentResp {
  InterfaceDocument document = 1;
}

message MockInterfaceDocumentReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string document_id = 2 [(validate.rules).string = {pattern: "(?:^interface_document_id:.+?)"}]; // 接口ID
  WithConfig with_config = 3 [(validate.rules).enum.defined_only = true]; // 是否使用接口配置
  string config_id = 4 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^interface_config_id:.+?)"}]; // 接口配置ID
}
message MockInterfaceDocumentResp {
  HttpRequestComponent mock = 1;
}

message SearchInterfaceDocumentReferenceReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string document_id = 2 [(validate.rules).string = {pattern: "(?:^interface_document_id:.+?)"}]; // 接口ID
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchInterfaceDocumentReferenceResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated SearchInterfaceDocumentReferenceItem items = 5;
}

message ModifyInterfaceDocumentReferenceStateReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string document_id = 2 [(validate.rules).string = {pattern: "(?:^interface_document_id:.+?)"}]; // 接口ID
  repeated string plan_ids = 3 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^plan_id:.+?)"}}}]; // 计划ID列表
  CommonState state = 4 [(validate.rules).enum = {in: [1, 2]}]; // 引用状态
}
message ModifyInterfaceDocumentReferenceStateResp {}

message CreateInterfaceSchemaReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  string full_name = 3 [(validate.rules).string = {min_len: 1, max_len: 128}]; // 数据模型完整名称
  string name = 4 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 数据模型名称
  string description = 5 [(validate.rules).string.max_len = 255]; // 数据模型描述
  Schema data = 6 [(validate.rules).message.required = true]; // 数据模型数据
}
message CreateInterfaceSchemaResp {
  InterfaceSchema schema = 1;
}

message RemoveInterfaceSchemaReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string schema_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^interface_schema_id:.+?)"}}}]; // 接口数据模型ID列表
}
message RemoveInterfaceSchemaResp {}

message ModifyInterfaceSchemaReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  string schema_id = 3 [(validate.rules).string = {pattern: "(?:^interface_schema_id:.+?)"}]; // 数据模型ID
  string name = 4 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 数据模型名称
  string description = 5 [(validate.rules).string.max_len = 255]; // 数据模型描述
  Schema data = 6 [(validate.rules).message.required = true]; // 数据模型数据
}
message ModifyInterfaceSchemaResp {
  InterfaceSchema schema = 1;
}

message SearchInterfaceSchemaReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchInterfaceSchemaResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated InterfaceSchema items = 5;
}

message ViewInterfaceSchemaReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string schema_id = 2 [(validate.rules).string = {pattern: "(?:^interface_schema_id:.+?)"}]; // 接口数据模型ID
}
message ViewInterfaceSchemaResp {
  InterfaceSchema schema = 1;
}

message CreateInterfaceConfigReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string document_id = 2 [(validate.rules).string = {pattern: "(?:^interface_document_id:.+?)"}]; // 接口ID
  string name = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 接口配置名称
  string description = 4 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 接口配置描述
  string path = 5 [(validate.rules).string = {min_len: 1, max_len: 128}]; // 接口配置的请求路径
  string method = 6 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 接口配置的请求方法
  Document data = 7 [(validate.rules).message.required = true]; // 接口配置的数据
  repeated InputParameter input_parameters = 8 [json_name = "imports", (validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 接口配置的输入参数
  repeated OutputParameter output_parameters = 9 [json_name = "exports", (validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 接口配置的输出参数
}
message CreateInterfaceConfigResp {
  InterfaceConfig config = 1;
}

message RemoveInterfaceConfigReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string document_id = 2 [(validate.rules).string = {pattern: "(?:^interface_document_id:.+?)"}]; // 接口ID
  repeated string config_ids = 3 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^interface_config_id:.+?)"}}}]; // 接口配置ID列表
}
message RemoveInterfaceConfigResp {}

message ModifyInterfaceConfigReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string document_id = 2 [(validate.rules).string = {pattern: "(?:^interface_document_id:.+?)"}]; // 接口ID
  string config_id = 3 [(validate.rules).string = {pattern: "(?:^interface_config_id:.+?)"}]; // 接口配置ID
  string name = 4 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 接口配置名称
  string description = 5 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 接口配置描述
  string path = 6 [(validate.rules).string = {min_len: 1, max_len: 128}]; // 接口配置的请求路径
  string method = 7 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 接口配置的请求方法
  Document data = 8 [(validate.rules).message.required = true]; // 接口配置的数据
  repeated InputParameter input_parameters = 9 [json_name = "imports", (validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 接口配置的输入参数
  repeated OutputParameter output_parameters = 10 [json_name = "exports", (validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 接口配置的输出参数
}
message ModifyInterfaceConfigResp {
  InterfaceConfig config = 1;
}

message SearchInterfaceConfigReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string document_id = 2 [(validate.rules).string = {pattern: "(?:^interface_document_id:.+?)"}]; // 接口ID
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchInterfaceConfigResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated InterfaceConfig items = 5;
}

message ViewInterfaceConfigReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string document_id = 2 [(validate.rules).string = {pattern: "(?:^interface_document_id:.+?)"}]; // 接口ID
  string config_id = 3 [(validate.rules).string = {pattern: "(?:^interface_config_id:.+?)"}]; // 接口配置ID
}
message ViewInterfaceConfigResp {
  InterfaceConfig config = 1;
}

message CreateInterfaceCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string document_id = 2 [(validate.rules).string = {pattern: "(?:^interface_document_id:.+?)"}]; // 接口ID
  string name = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 接口用例名称
  string description = 4 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 接口用例描述
  int64 priority = 5 [(validate.rules).int64.gte = 0]; // 优先级
  repeated string tags = 6 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len:1, max_len: 64}}}]; // 接口用例标签
  AccountConfig account_config = 7;
  repeated Node nodes = 8 [(validate.rules).repeated.min_items = 2]; // 接口用例画布中的节点列表
  repeated Edge edges = 9 [(validate.rules).repeated.min_items = 1]; // 接口用例画布中的线段列表
  repeated Combo combos = 10 [(validate.rules).repeated = {ignore_empty: true, min_items: 0}]; // 接口用例画布中的组合列表
  repeated Relation relations = 11 [(validate.rules).repeated.min_items = 2]; // 接口用例画布中的节点与组合的关系列表
  string maintained_by = 12 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 接口用例维护者
}
message CreateInterfaceCaseResp {
  InterfaceCase case = 1;
}

message RemoveInterfaceCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string document_id = 2 [(validate.rules).string = {pattern: "(?:^interface_document_id:.+?)"}]; // 接口ID
  repeated string case_ids = 3 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^interface_case_id:.+?)"}}}]; // 接口用例ID列表
}
message RemoveInterfaceCaseResp {}

message ModifyInterfaceCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string document_id = 2 [(validate.rules).string = {pattern: "(?:^interface_document_id:.+?)"}]; // 接口ID
  string case_id = 3 [(validate.rules).string = {pattern: "(?:^interface_case_id:.+?)"}]; // 接口用例ID
  string name = 4 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 接口用例名称
  string description = 5 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 接口用例描述
  int64 priority = 6 [(validate.rules).int64.gte = 0]; // 优先级
  repeated string tags = 7 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len:1, max_len: 64}}}]; // 接口用例标签
  //CommonState state = 8 [(validate.rules).enum = {in: [1, 2]}]; // 接口用例状态
  AccountConfig account_config = 9;
  repeated Node nodes = 10 [(validate.rules).repeated.min_items = 2]; // 接口用例画布中的节点列表
  repeated Edge edges = 11 [(validate.rules).repeated.min_items = 1]; // 接口用例画布中的线段列表
  repeated Combo combos = 12 [(validate.rules).repeated = {ignore_empty: true, min_items: 0}]; // 接口用例画布中的组合列表
  repeated Relation relations = 13 [(validate.rules).repeated.min_items = 2]; // 接口用例画布中的节点与组合的关系列表
  string maintained_by = 14 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 接口用例维护者
}
message ModifyInterfaceCaseResp {
  InterfaceCase case = 1;
}

message SearchInterfaceCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string document_id = 2 [(validate.rules).string = {pattern: "(?:^interface_document_id:.+?)"}]; // 接口ID
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchInterfaceCaseResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated InterfaceCase items = 5;
}

message ViewInterfaceCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string document_id = 2; // 接口ID
  string case_id = 3 [(validate.rules).string = {pattern: "(?:^interface_case_id:.+?)"}]; // 接口用例ID
  string version = 4 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^version:.+?)"}]; // 接口用例版本
}
message ViewInterfaceCaseResp {
  InterfaceCase case = 1;
}

message MaintainInterfaceCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string document_id = 2 [(validate.rules).string = {pattern: "(?:^interface_document_id:.+?)"}]; // 接口ID
  string case_id = 3 [(validate.rules).string = {pattern: "(?:^interface_case_id:.+?)"}]; // 接口用例ID
  string maintained_by = 4 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // API用例维护者
}
message MaintainInterfaceCaseResp {
  InterfaceCase case = 1;
}

message PublishInterfaceCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string document_id = 2 [(validate.rules).string = {pattern: "(?:^interface_document_id:.+?)"}]; // 接口ID
  string case_id = 3 [(validate.rules).string = {pattern: "(?:^interface_case_id:.+?)"}]; // 接口用例ID
}
message PublishInterfaceCaseResp {
  InterfaceCase case = 1;
}

message SearchInterfaceCaseReferenceReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string case_id = 2; // 用例ID
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchInterfaceCaseReferenceResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated SearchInterfaceCaseReferenceItem items = 5;
}

message UpdateInterfaceCoverageDataReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  int64 keep_days = 2 [(validate.rules).int64 = {ignore_empty: true, gte: 1, lte: 365}]; // 保留天数
}
message UpdateInterfaceCoverageDataResp {}

message GetInterfaceCoverageTeamsReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
}
message GetInterfaceCoverageTeamsResp {
  repeated string teams = 1;
}

message GetInterfaceCoverageDataReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string team = 2 [(validate.rules).string = {min_len: 1, max_len: 128}]; // 团队名称
  string from = 3 [(validate.rules).string = {pattern: "\\d{4}-\\d{2}-\\d{2}"}]; // 开始日期
  string to = 4 [(validate.rules).string = {pattern: "\\d{4}-\\d{2}-\\d{2}"}]; // 结束日期
}
message GetInterfaceCoverageDataResp {
  InterfaceCoverageData data = 1;
}

message UpdateInterfaceDocumentTagsReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
}
message UpdateInterfaceDocumentTagsResp {}


//ComponentGroupService 组件组服务
service ComponentGroupService {
  //CreateComponentGroup 创建组件组
  rpc CreateComponentGroup(CreateComponentGroupReq) returns (CreateComponentGroupResp);
  //RemoveComponentGroup 删除组件组
  rpc RemoveComponentGroup(RemoveComponentGroupReq) returns (RemoveComponentGroupResp);
  //ModifyComponentGroup 编辑组件组
  rpc ModifyComponentGroup(ModifyComponentGroupReq) returns (ModifyComponentGroupResp);
  //SearchComponentGroup 搜索组件组
  rpc SearchComponentGroup(SearchComponentGroupReq) returns (SearchComponentGroupResp);
  //ViewComponentGroup 查看组件组
  rpc ViewComponentGroup(ViewComponentGroupReq) returns (ViewComponentGroupResp);
  //SearchComponentGroupReference 搜索组件组引用详情
  rpc SearchComponentGroupReference(SearchComponentGroupReferenceReq) returns (SearchComponentGroupReferenceResp);
}

message CreateComponentGroupReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  string component_group_type = 3 [(validate.rules).string = {in: ["SINGLE", "GROUP", "SETUP", "TEARDOWN"]}]; // 组件组类型
  string name = 4 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 组件组名称
  string description = 5 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 组件组描述
  int64 priority = 6 [(validate.rules).int64.gte = 0]; // 优先级
  repeated string tags = 7 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len:1, max_len: 64}}}]; // 组件组标签
  repeated Import imports = 8 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 组件组的入参列表
  repeated Export exports = 9 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 组件组的出参列表
  AccountConfig account_config = 10;
  repeated Node nodes = 11 [(validate.rules).repeated.min_items = 2]; // 组件组画布中的节点列表
  repeated Edge edges = 12 [(validate.rules).repeated.min_items = 1]; // 组件组画布中的线段列表
  repeated Combo combos = 13 [(validate.rules).repeated = {ignore_empty: true, min_items: 0}]; // 组件组画布中的组合列表
  repeated Relation relations = 14 [(validate.rules).repeated.min_items = 2]; // 组件组画布中的节点与组合的关系列表
  Relation reference_relations = 15 [(validate.rules).message.required = true]; // 组件组画布中引用时涉及的节点与组合的关系
  string maintained_by = 16 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 组件组维护者
}
message CreateComponentGroupResp {
  ComponentGroup component_group = 1;
}

message RemoveComponentGroupReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string component_group_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^component_group_id:.+?)"}}}]; // 组件组ID列表
}
message RemoveComponentGroupResp {}

message ModifyComponentGroupReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  string component_group_id = 3 [(validate.rules).string = {pattern: "(?:^component_group_id:.+?)"}]; // 组件组ID
  string component_group_type = 4 [(validate.rules).string = {in: ["SINGLE", "GROUP", "SETUP", "TEARDOWN"]}]; // 组件组类型
  string name = 5 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 组件组名称
  string description = 6 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 组件组描述
  int64 priority = 7 [(validate.rules).int64.gte = 0]; // 优先级
  repeated string tags = 8 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len:1, max_len: 64}}}]; // 组件组标签
  repeated Import imports = 9 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 组件组的入参列表
  repeated Export exports = 10 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 组件组的出参列表
  AccountConfig account_config = 11;
  repeated Node nodes = 12 [(validate.rules).repeated.min_items = 2]; // 组件组画布中的节点列表
  repeated Edge edges = 13 [(validate.rules).repeated.min_items = 1]; // 组件组画布中的线段列表
  repeated Combo combos = 14 [(validate.rules).repeated = {ignore_empty: true, min_items: 0}]; // 组件组画布中的组合列表
  repeated Relation relations = 15 [(validate.rules).repeated.min_items = 2]; // 组件组画布中的节点与组合的关系列表
  Relation reference_relations = 16 [(validate.rules).message.required = true]; // 组件组画布中引用时涉及的节点与组合的关系
  string maintained_by = 17 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 组件组维护者
}
message ModifyComponentGroupResp {
  ComponentGroup component_group = 1;
}

message SearchComponentGroupReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchComponentGroupResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated ComponentGroup items = 5;
}

message ViewComponentGroupReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string component_group_id = 2 [(validate.rules).string = {pattern: "(?:^component_group_id:.+?)"}]; // 组件组ID
  string version = 3; // 版本
  bool only_info = 4;
}
message ViewComponentGroupResp {
  ComponentGroup component_group = 1;
  repeated Referenced referenced_relations = 2;
}

message SearchComponentGroupReferenceReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string component_group_id = 2 [(validate.rules).string = {pattern: "(?:^component_group_id:.+?)"}]; // 组件组ID
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchComponentGroupReferenceResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated SearchComponentGroupReferenceItem items = 5;
}


//ApiCaseService API用例服务
service ApiCaseService {
  //CreateApiCase 创建API用例
  rpc CreateApiCase(CreateApiCaseReq) returns (CreateApiCaseResp);
  //RemoveApiCase 删除API用例
  rpc RemoveApiCase(RemoveApiCaseReq) returns (RemoveApiCaseResp);
  //ModifyApiCase 编辑API用例
  rpc ModifyApiCase(ModifyApiCaseReq) returns (ModifyApiCaseResp);
  //SearchApiCase 搜索API用例
  rpc SearchApiCase(SearchApiCaseReq) returns (SearchApiCaseResp);
  //ViewApiCase 查看API用例
  rpc ViewApiCase(ViewApiCaseReq) returns (ViewApiCaseResp);
  //SearchApiCaseReference 搜索API用例引用详情
  rpc SearchApiCaseReference(SearchApiCaseReferenceReq) returns (SearchApiCaseReferenceResp);
  //MaintainApiCase 维护API用例
  rpc MaintainApiCase(MaintainApiCaseReq) returns (MaintainApiCaseResp);
  //PublishApiCase 发布API用例
  rpc PublishApiCase(PublishApiCaseReq) returns (PublishApiCaseResp);
}

message CreateApiCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  string name = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // API用例名称
  string description = 4 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // API用例描述
  int64 priority = 5 [(validate.rules).int64.gte = 0]; // 优先级
  repeated string tags = 6 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len:1, max_len: 64}}}]; // API用例标签
  AccountConfig account_config = 7;
  repeated Node nodes = 8 [(validate.rules).repeated.min_items = 2]; // API用例画布中的节点列表
  repeated Edge edges = 9 [(validate.rules).repeated.min_items = 1]; // API用例画布中的线段列表
  repeated Combo combos = 10 [(validate.rules).repeated = {ignore_empty: true, min_items: 0}]; // API用例画布中的组合列表
  repeated Relation relations = 11 [(validate.rules).repeated.min_items = 2]; // API用例画布中的节点与组合的关系列表
  string maintained_by = 12 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // API用例维护者
}
message CreateApiCaseResp {
  ApiCase case = 1;
}

message RemoveApiCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string case_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^case_id:.+?)"}}}]; // API用例ID列表
}
message RemoveApiCaseResp {}

message ModifyApiCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  string case_id = 3 [(validate.rules).string = {pattern: "(?:^case_id:.+?)"}]; // API用例ID
  string name = 4 [(validate.rules).string = {min_len: 1, max_len: 64}]; // API用例名称
  string description = 5 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // API用例描述
  int64 priority = 6 [(validate.rules).int64.gte = 0]; // 优先级
  repeated string tags = 7 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len:1, max_len: 64}}}]; // API用例标签
  //CommonState state = 8 [(validate.rules).enum = {in: [1, 2]}]; // API用例状态
  AccountConfig account_config = 9;
  repeated Node nodes = 10 [(validate.rules).repeated.min_items = 2]; // API用例画布中的节点列表
  repeated Edge edges = 11 [(validate.rules).repeated.min_items = 1]; // API用例画布中的线段列表
  repeated Combo combos = 12 [(validate.rules).repeated = {ignore_empty: true, min_items: 0}]; // API用例画布中的组合列表
  repeated Relation relations = 13 [(validate.rules).repeated.min_items = 2]; // API用例画布中的节点与组合的关系列表
  string maintained_by = 14 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // API用例维护者
}
message ModifyApiCaseResp {
  ApiCase case = 1;
}

message SearchApiCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchApiCaseResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated ApiCase items = 5;
}

message ViewApiCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string case_id = 2 [(validate.rules).string = {pattern: "(?:^case_id:.+?)"}]; // 用例ID
  string version = 3; // 版本
}
message ViewApiCaseResp {
  ApiCase case = 1;
}

message SearchApiCaseReferenceReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string case_id = 2 [(validate.rules).string = {pattern: "(?:^case_id:.+?)"}]; // 用例ID
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchApiCaseReferenceResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated SearchApiCaseReferenceItem items = 5;
}

message MaintainApiCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string case_id = 2 [(validate.rules).string = {pattern: "(?:^case_id:.+?)"}]; // API用例ID
  string maintained_by = 3 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // API用例维护者
}
message MaintainApiCaseResp {
  ApiCase case = 1;
}

message PublishApiCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string case_id = 2 [(validate.rules).string = {pattern: "(?:^case_id:.+?)"}]; // API用例ID
}
message PublishApiCaseResp {
  ApiCase case = 1;
}


//ApiSuiteService API集合服务
service ApiSuiteService {
  //CreateApiSuite 创建API集合
  rpc CreateApiSuite(CreateApiSuiteReq) returns (CreateApiSuiteResp);
  //RemoveApiSuite 删除API集合
  rpc RemoveApiSuite(RemoveApiSuiteReq) returns (RemoveApiSuiteResp);
  //ModifyApiSuite 编辑API集合
  rpc ModifyApiSuite(ModifyApiSuiteReq) returns (ModifyApiSuiteResp);
  //SearchApiSuite 搜索API集合
  rpc SearchApiSuite(SearchApiSuiteReq) returns (SearchApiSuiteResp);
  //ViewApiSuite 查看API集合
  rpc ViewApiSuite(ViewApiSuiteReq) returns (ViewApiSuiteResp);
  //SearchApiCaseInApiSuite 搜索API集合中的API用例
  rpc SearchApiCaseInApiSuite(SearchApiCaseInApiSuiteReq) returns (SearchApiCaseInApiSuiteResp);
  //SearchApiCaseNotInApiSuite 搜索不在指定的API集合中的API用例
  rpc SearchApiCaseNotInApiSuite(SearchApiCaseNotInApiSuiteReq) returns (SearchApiCaseNotInApiSuiteResp);
  //AddApiCaseToApiSuite 添加API用例到API集合中
  rpc AddApiCaseToApiSuite(AddApiCaseToApiSuiteReq) returns (AddApiCaseToApiSuiteResp);
  //RemoveApiCaseFromApiSuite 从API集合中移除API用例
  rpc RemoveApiCaseFromApiSuite(RemoveApiCaseFromApiSuiteReq) returns (RemoveApiCaseFromApiSuiteResp);
  //SearchApiSuiteReference 搜索API集合引用详情
  rpc SearchApiSuiteReference(SearchApiSuiteReferenceReq) returns (SearchApiSuiteReferenceResp);
  //ModifyApiSuiteReferenceState 修改API集合所在的API计划的引用状态
  rpc ModifyApiSuiteReferenceState(ModifyApiSuiteReferenceStateReq) returns (ModifyApiSuiteReferenceStateResp);
  //SearchCaseInApiSuite 搜索API集合中的用例
  rpc SearchCaseInApiSuite(SearchCaseInApiSuiteReq) returns (SearchCaseInApiSuiteResp);
  //AddCaseToApiSuite 添加用例到API集合中
  rpc AddCaseToApiSuite(AddCaseToApiSuiteReq) returns (AddCaseToApiSuiteResp);
  //RemoveCaseFromApiSuite 从API集合中移除用例
  rpc RemoveCaseFromApiSuite(RemoveCaseFromApiSuiteReq) returns (RemoveCaseFromApiSuiteResp);
  //SearchServiceCaseNotInApiSuite 搜索不在指定的API集合中的精准测试用例
  rpc SearchServiceCaseNotInApiSuite(SearchServiceCaseNotInApiSuiteReq) returns (SearchServiceCaseNotInApiSuiteResp);
}

message CreateApiSuiteReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  string name = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 集合名称
  string description = 4 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 集合描述
  int64 priority = 5 [(validate.rules).int64.gte = 0]; // 优先级
  repeated string tags = 6 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len:1, max_len: 64}}}]; // 标签
  int64 case_execution_mode = 7 [(validate.rules).int64 = {in: [1, 2]}]; // 用例执行方式
  string maintained_by = 8 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 维护者
  // repeated string case_ids = 9 [(validate.rules).repeated = {ignore_empty: true, unique: true, items: {string: {min_len: 1, max_len: 64, pattern: "(?:^case_id:.+?)"}}}]; // 导入的API用例ID列表
  repeated CaseTypeId case_ids = 10 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 用例列表
}
message CreateApiSuiteResp {
  ApiSuite suite = 1;
}

message RemoveApiSuiteReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string suite_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^suite_id:.+?)"}}}]; // 集合ID列表
}
message RemoveApiSuiteResp {}

message ModifyApiSuiteReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  string suite_id = 3 [(validate.rules).string = {pattern: "(?:^suite_id:.+?)"}]; // 集合ID
  string name = 4 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 集合名称
  string description = 5 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 集合描述
  int64 priority = 6 [(validate.rules).int64.gte = 0]; // 优先级
  repeated string tags = 7 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len:1, max_len: 64}}}]; // 标签
  CommonState state = 8 [(validate.rules).enum = {in: [1, 2]}]; // API集合状态
  ExecutionMode case_execution_mode = 9 [(validate.rules).enum = {in: [1, 2]}]; // 用例执行方式
  string maintained_by = 10 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 维护者
}
message ModifyApiSuiteResp {
  ApiSuite suite = 1;
}

message SearchApiSuiteReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchApiSuiteResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated ApiSuite items = 5;
}

message ViewApiSuiteReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string suite_id = 2 [(validate.rules).string = {pattern: "(?:^suite_id:.+?)"}]; // 集合ID
}
message ViewApiSuiteResp {
  ApiSuite suite = 1;
}

message SearchApiCaseInApiSuiteReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string suite_id = 2 [(validate.rules).string = {pattern: "(?:^suite_id:.+?)"}]; // 集合ID
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchApiCaseInApiSuiteResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated ApiCase items = 5;
}

message SearchApiCaseNotInApiSuiteReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string suite_id = 2 [(validate.rules).string = {pattern: "(?:^suite_id:.+?)"}]; // 集合ID
  string category_id = 3 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 用例分类树的分类ID
  sqlbuilder.Condition condition = 4; // 查询条件
  sqlbuilder.Pagination pagination = 5; // 查询分页
  repeated sqlbuilder.SortField sort = 6 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchApiCaseNotInApiSuiteResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated ApiCase items = 5;
}

message AddApiCaseToApiSuiteReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string suite_id = 2 [(validate.rules).string = {pattern: "(?:^suite_id:.+?)"}]; // 集合ID
  repeated string case_ids = 3 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^case_id:.+?)"}}}]; // 用例ID列表
}
message AddApiCaseToApiSuiteResp {}

message RemoveApiCaseFromApiSuiteReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string suite_id = 2 [(validate.rules).string = {pattern: "(?:^suite_id:.+?)"}]; // 集合ID
  repeated string case_ids = 3 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^case_id:.+?)"}}}]; // 用例ID列表
}
message RemoveApiCaseFromApiSuiteResp {}

message SearchApiSuiteReferenceReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string suite_id = 2 [(validate.rules).string = {pattern: "(?:^suite_id:.+?)"}]; // 集合ID
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchApiSuiteReferenceResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated SearchApiSuiteReferenceItem items = 5;
}

message ModifyApiSuiteReferenceStateReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string suite_id = 2 [(validate.rules).string = {pattern: "(?:^suite_id:.+?)"}]; // 集合ID
  repeated string plan_ids = 3 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^plan_id:.+?)"}}}]; // 计划ID列表
  CommonState state = 4 [(validate.rules).enum = {in: [1, 2]}]; // 引用状态
}
message ModifyApiSuiteReferenceStateResp {}

message SearchCaseInApiSuiteReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string suite_id = 2 [(validate.rules).string = {pattern: "(?:^suite_id:.+?)"}]; // 集合ID
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchCaseInApiSuiteResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated SearchCaseInApiSuiteItem items = 5;
}

message AddCaseToApiSuiteReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string suite_id = 2 [(validate.rules).string = {pattern: "(?:^suite_id:.+?)"}]; // 集合ID
  repeated CaseTypeId case_ids = 3 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 用例列表
}
message AddCaseToApiSuiteResp {}

message RemoveCaseFromApiSuiteReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string suite_id = 2 [(validate.rules).string = {pattern: "(?:^suite_id:.+?)"}]; // 集合ID
  repeated CaseTypeId case_ids = 3 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 用例列表
}
message RemoveCaseFromApiSuiteResp {}

message SearchServiceCaseNotInApiSuiteReq{
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string suite_id = 2 [(validate.rules).string = {pattern: "(?:^suite_id:.+?)"}]; // 集合ID
  repeated string services = 3 [(validate.rules).repeated = {ignore_empty: true, unique: true, items: {string: {min_len:1, max_len: 64}}}]; // 服务列表
  sqlbuilder.Condition condition = 4; // 查询条件
  sqlbuilder.Pagination pagination = 5; // 查询分页
  repeated sqlbuilder.SortField sort = 6 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchServiceCaseNotInApiSuiteResp{
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated SearchCaseInApiSuiteItem items = 5;
}

//ApiPlanService API计划服务
service ApiPlanService {
  //CreateApiPlan 创建API计划
  rpc CreateApiPlan(CreateApiPlanReq) returns (CreateApiPlanResp);
  //RemoveApiPlan 删除API计划
  rpc RemoveApiPlan(RemoveApiPlanReq) returns (RemoveApiPlanResp);
  //ModifyApiPlan 编辑API计划
  rpc ModifyApiPlan(ModifyApiPlanReq) returns (ModifyApiPlanResp);
  //SearchApiPlan 搜索API计划
  rpc SearchApiPlan(SearchApiPlanReq) returns (SearchApiPlanResp);
  //ViewApiPlan 查看API计划
  rpc ViewApiPlan(ViewApiPlanReq) returns (ViewApiPlanResp);
  //SearchSuiteInApiPlan 搜索API计划中的集合（包括：场景集合和接口集合）
  rpc SearchSuiteInApiPlan(SearchSuiteInApiPlanReq) returns (SearchSuiteInApiPlanResp);
  //SearchSuiteNotInApiPlan 搜索不在指定的API计划中的集合（包括：场景集合和接口集合）
  rpc SearchSuiteNotInApiPlan(SearchSuiteNotInApiPlanReq) returns (SearchSuiteNotInApiPlanResp);
  //AddSuiteToApiPlan 添加集合到API计划中
  rpc AddSuiteToApiPlan(AddSuiteToApiPlanReq) returns (AddSuiteToApiPlanResp);
  //RemoveSuiteFromApiPlan 移除API计划中的集合
  rpc RemoveSuiteFromApiPlan(RemoveSuiteFromApiPlanReq) returns (RemoveSuiteFromApiPlanResp);
  //SearchCaseInApiPlan 搜索API计划中指定集合中的用例
  rpc SearchCaseInApiPlan(SearchCaseInApiPlanReq) returns (SearchCaseInApiPlanResp);
  //ModifyApiPlanReferenceState 修改API计划执行数据的引用状态（包括：集合以及集合中的用例）
  rpc ModifyApiPlanReferenceState(ModifyApiPlanReferenceStateReq) returns (ModifyApiPlanReferenceStateResp);
  // AdvancedSearchSuiteNotInApiPlan 高级搜索接口文档
  rpc AdvancedSearchSuiteNotInApiPlan(AdvancedSearchSuiteNotInApiPlanReq) returns (AdvancedSearchSuiteNotInApiPlanResp);
	// SearchLikeApiPlan 搜索API计划
	rpc SearchLikeApiPlan(SearchApiPlanReq) returns (SearchApiPlanResp);
}

message CreateApiPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 64}]; // API计划名称
  string description = 3 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // API计划描述
  int64 priority = 4 [(validate.rules).int64.gte = 0]; // 优先级
  repeated string tags = 5 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len:1, max_len: 64}}}]; // 标签
  common.TriggerMode type = 6 [(validate.rules).enum.defined_only = true]; // 计划类型
  common.PurposeType purpose = 7 [(validate.rules).enum.defined_only = true]; // 计划用途
  string cron_expression = 8 [(validate.rules).string = {ignore_empty: true}]; // 定时触发的Cron表达式
  string general_config_id = 9 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^general_config_id:.+?)"}]; // 通用配置ID
  repeated string account_config_ids = 10 [(validate.rules).repeated = {ignore_empty: true, items: {string: {pattern: "(?:^account_config_id:.+?)"}}}]; // 池账号配置ID列表
  ExecutionMode suite_execution_mode = 11 [(validate.rules).enum = {in: [1, 2]}]; // 集合执行方式
  ExecutionMode case_execution_mode = 12 [(validate.rules).enum = {in: [1, 2]}]; // 用例执行方式
  string maintained_by = 13 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 维护者
  repeated SuiteTypeId suite_ids = 14 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 导入的集合ID列表
  string category_id = 15 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
}
message CreateApiPlanResp {
  ApiPlan plan = 1;
}

message RemoveApiPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string plan_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^plan_id:.+?)"}}}]; // 计划ID列表
}
message RemoveApiPlanResp {}

message ModifyApiPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^plan_id:.+?)"}]; // API计划ID
  string name = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // API计划名称
  string description = 4 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // API计划描述
  int64 priority = 5 [(validate.rules).int64.gte = 0]; // 优先级
  repeated string tags = 6 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len:1, max_len: 64}}}]; // 标签
  CommonState state = 7 [(validate.rules).enum = {in: [1, 2]}]; // API计划状态
  common.TriggerMode type = 8 [(validate.rules).enum.defined_only = true]; // 计划类型
  common.PurposeType purpose = 9 [(validate.rules).enum.defined_only = true]; // 计划用途
  string cron_expression = 10 [(validate.rules).string = {ignore_empty: true}]; // 定时触发的Cron表达式
  string general_config_id = 11 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^general_config_id:.+?)"}]; // 通用配置ID
  repeated string account_config_ids = 12 [(validate.rules).repeated = {ignore_empty: true, items: {string: {pattern: "(?:^account_config_id:.+?)"}}}]; // 池账号配置ID列表
  ExecutionMode suite_execution_mode = 13 [(validate.rules).enum = {in: [1, 2]}]; // 集合执行方式
  ExecutionMode case_execution_mode = 14 [(validate.rules).enum = {in: [1, 2]}]; // 用例执行方式
  string maintained_by = 15 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 维护者
  string category_id = 16 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
}
message ModifyApiPlanResp {
  ApiPlan plan = 1;
}

message SearchApiPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  sqlbuilder.Condition condition = 2; // 查询条件
  sqlbuilder.Pagination pagination = 3; // 查询分页
  repeated sqlbuilder.SortField sort = 4 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
  string category_id = 5; // 分类ID
}
message SearchApiPlanResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated SearchApiPlanItem items = 5;
}

message ViewApiPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^plan_id:.+?)"}]; // 计划ID
}
message ViewApiPlanResp {
  ApiPlan plan = 1;
}

message SearchSuiteInApiPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^plan_id:.+?)"}]; // 计划ID
  string suite_type = 3 [(validate.rules).string = {ignore_empty: true, in: ["API_SUITE", "INTERFACE_DOCUMENT"]}]; // 集合类型
  sqlbuilder.Condition condition = 4; // 查询条件
  sqlbuilder.Pagination pagination = 5; // 查询分页
  repeated sqlbuilder.SortField sort = 6 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchSuiteInApiPlanResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated SearchSuiteInApiPlanItem items = 5;
}

message SearchSuiteNotInApiPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^plan_id:.+?)"}]; // 计划ID
  string suite_type = 3 [(validate.rules).string = {ignore_empty: true, in: ["API_SUITE", "INTERFACE_DOCUMENT"]}]; // 集合类型
  string category_id = 4 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 集合分类树的分类ID
  sqlbuilder.Condition condition = 5; // 查询条件
  sqlbuilder.Pagination pagination = 6; // 查询分页
  repeated sqlbuilder.SortField sort = 7 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchSuiteNotInApiPlanResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated SearchSuiteNotInApiPlanItem items = 5;
}

message AddSuiteToApiPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^plan_id:.+?)"}]; // 计划ID
  repeated SuiteTypeId suite_ids = 3 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 集合类型与ID列表
}
message AddSuiteToApiPlanResp {}

message RemoveSuiteFromApiPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^plan_id:.+?)"}]; // 计划ID
  repeated SuiteTypeId suite_ids = 3 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 集合类型与ID列表
}
message RemoveSuiteFromApiPlanResp {}

message SearchCaseInApiPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^plan_id:.+?)"}]; // 计划ID
  string suite_type = 3 [(validate.rules).string = {in: ["API_SUITE", "INTERFACE_DOCUMENT"]}]; // 集合类型
  string suite_id = 4 [(validate.rules).string = {pattern: "(?:^suite_id:.+?|^interface_document_id:.+?)"}]; // 集合ID
  sqlbuilder.Condition condition = 5; // 查询条件
  sqlbuilder.Pagination pagination = 6; // 查询分页
  repeated sqlbuilder.SortField sort = 7 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchCaseInApiPlanResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated SearchCaseInApiPlanItem items = 5;
}

message ModifyApiPlanReferenceStateReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^plan_id:.+?)"}]; // 计划ID
  string suite_type = 3 [(validate.rules).string = {in: ["API_SUITE", "INTERFACE_DOCUMENT"]}]; // 集合类型
  string suite_id = 4 [(validate.rules).string = {pattern: "(?:^suite_id:.+?|^interface_document_id:.+?)"}]; // 集合ID
  string case_id = 5 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^case_id:.+?|^interface_case_id:.+?)"}]; // 用例ID
  CommonState state = 6 [(validate.rules).enum = {in: [1, 2]}]; // 引用状态
  string case_type = 7 [(validate.rules).string = {ignore_empty: true, in: ["API_CASE", "INTERFACE_CASE"]}]; // 用例类型
}
message ModifyApiPlanReferenceStateResp {}

service AdvancedSearchService {
  rpc SearchAdvancedSearchField(SearchAdvancedSearchFieldReq) returns (SearchAdvancedSearchFieldResp);
  rpc SearchAdvancedSearchCondition(SearchAdvancedSearchConditionReq) returns (SearchAdvancedSearchConditionResp);
}

message SearchAdvancedSearchFieldReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  SceneType scene_type = 2 [(validate.rules).enum = {in: [1, 2]}]; // 场景类型
}
message SearchAdvancedSearchFieldResp {
  repeated AdvancedSearchField items = 1;
}

message SearchAdvancedSearchConditionReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string field_id = 2 [(validate.rules).string = {pattern: "(?:^field_id:.+?|^1$)"}]; // 字段ID
}
message SearchAdvancedSearchConditionResp {
  repeated AdvancedSearchCondition items = 1;
}

message AdvancedSearchSuiteNotInApiPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^plan_id:.+?)"}]; // 计划ID
  string suite_type = 3 [(validate.rules).string = {in: ["API_SUITE", "INTERFACE_DOCUMENT"]}]; // 集合类型
  sqlbuilder.Condition condition = 4; // 查询条件
  sqlbuilder.Pagination pagination = 5; // 查询分页
  repeated sqlbuilder.SortField sort = 6 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message AdvancedSearchSuiteNotInApiPlanResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated AdvancedSearchSuiteItem items = 5;
}


service NotifyService {
  rpc CreatePlanNotify(CreatePlanNotifyReq) returns (CreatePlanNotifyResp);
  rpc RemovePlanNotify(RemovePlanNotifyReq) returns (RemovePlanNotifyResp);
  rpc ModifyPlanNotify(ModifyPlanNotifyReq) returns (ModifyPlanNotifyResp);
  rpc SearchPlanNotify(SearchPlanNotifyReq) returns (SearchPlanNotifyResp);
  rpc GetPlanNotify(GetPlanNotifyReq) returns (GetPlanNotifyResp);
}

message CreatePlanNotifyReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^plan_id:.+?|^ui_plan_id:.+?|^perf_plan_id:.+?|^stability_plan_id:.+?)"}]; // 计划ID
  NotifyMode notify_mode = 3 [(validate.rules).enum = {not_in: [0]}];
  NotifyType notify_type = 4 [(validate.rules).enum = {not_in: [0]}];
  repeated CreateNotifyItem receiver_infos = 5 [(validate.rules).repeated = {min_items: 1, items: {message: {required: true}}}];
}
message CreatePlanNotifyResp {
  repeated string ids = 1;
}

message RemovePlanNotifyReq {
  string project_id = 1;
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^plan_id:.+?|^ui_plan_id:.+?|^perf_plan_id:.+?|^stability_plan_id:.+?)"}]; // 计划ID
  repeated string notify_ids = 3 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^notify_id:.+?)"}}}];
}
message RemovePlanNotifyResp {
}

message ModifyPlanNotifyReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string notify_id = 2 [(validate.rules).string = {pattern: "(?:^notify_id:.+?)"}]; // 通知ID;
  NotifyMode notify_mode = 3 [(validate.rules).enum = {not_in: [0]}];
  NotifyType notify_type = 4 [(validate.rules).enum = {not_in: [0]}];
  string receiver_name = 5 [(validate.rules).string = {max_len: 64}];
  string receiver = 6 [(validate.rules).string = {pattern: "(?:^https://open\\.feishu\\.cn/open-apis|[\\w]+@[A-Za-z0-9]+(.[A-Za-z0-9]+){1,2})|^oc_[a-z0-9]{32}$"}];
}
message ModifyPlanNotifyResp {
}

message SearchPlanNotifyReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^plan_id:.+?|^ui_plan_id:.+?|^perf_plan_id:.+?|^stability_plan_id:.+?)"}]; // 计划ID
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchPlanNotifyResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated Notify items = 5;
}

message GetPlanNotifyReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^plan_id:.+?|^ui_plan_id:.+?|^perf_plan_id:.+?|^stability_plan_id:.+?)"}]; // 计划ID
}
message GetPlanNotifyResp {
  repeated NotifyItem items = 1;
}


//GitConfigurationService Git配置服务
service GitConfigurationService {
  //CreateGitConfiguration 创建Git配置
  rpc CreateGitConfiguration(CreateGitConfigurationReq) returns (CreateGitConfigurationResp);
  //RemoveGitConfiguration 删除Git配置
  rpc RemoveGitConfiguration(RemoveGitConfigurationReq) returns (RemoveGitConfigurationResp);
  //ModifyGitConfiguration 编辑Git配置
  rpc ModifyGitConfiguration(ModifyGitConfigurationReq) returns (ModifyGitConfigurationResp);
  //SearchGitConfiguration 搜索Git配置
  rpc SearchGitConfiguration(SearchGitConfigurationReq) returns (SearchGitConfigurationResp);
  //ViewGitConfiguration 查看Git配置
  rpc ViewGitConfiguration(ViewGitConfigurationReq) returns (ViewGitConfigurationResp);
  //TestGitConfiguration 测试Git配置
  rpc TestGitConfiguration(TestGitConfigurationReq) returns (TestGitConfigurationResp);
  //SyncGitConfiguration 通过手动同步Git配置对应的Git项目测试数据
  rpc SyncGitConfiguration(SyncGitConfigurationReq) returns (SyncGitConfigurationResp);
  //SyncGitConfigurationByWebhook 通过Webhook同步Git配置对应的Git项目测试数据
  rpc SyncGitConfigurationByWebhook(SyncGitConfigurationByWebhookReq) returns (SyncGitConfigurationByWebhookResp);
}

message CreateGitConfigurationReq{
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string type = 2 [(validate.rules).string = {in: ["GitLab", "GitHub", "Gitee"]}]; // Git类型（GitLab、GitHub、Gitee）
  string name = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // Git配置名称
  string description = 4 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // Git配置描述
  string url = 5 [(validate.rules).string = {uri: true, max_len: 255}]; // Git项目URL（http）
  string access_token = 6 [(validate.rules).string = {max_len: 128}]; // Git项目访问令牌
  string branch = 7 [(validate.rules).string = {max_len: 128}]; // Git项目分支名称
  string purpose = 8 [(validate.rules).string = {in: ["API", "UI"]}]; // 用途
}
message CreateGitConfigurationResp{
  GitConfiguration configuration = 1;
}

message RemoveGitConfigurationReq{
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string config_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^git_config_id:.+?)"}}}]; // Git配置ID列表
}
message RemoveGitConfigurationResp{}

message ModifyGitConfigurationReq{
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string config_id = 2 [(validate.rules).string = {pattern: "(?:^git_config_id:.+?)"}]; // Git配置ID
  string name = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // Git配置名称
  string description = 4 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // Git配置描述
  string access_token = 5 [(validate.rules).string = {max_len: 128}]; // Git项目访问令牌
}
message ModifyGitConfigurationResp{
  GitConfiguration configuration = 1;
}

message SearchGitConfigurationReq{
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  sqlbuilder.Condition condition = 2; // 查询条件
  sqlbuilder.Pagination pagination = 3; // 查询分页
  repeated sqlbuilder.SortField sort = 4 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchGitConfigurationResp{
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated GitConfiguration items = 5;
}

message ViewGitConfigurationReq{
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string config_id = 2 [(validate.rules).string = {pattern: "(?:^git_config_id:.+?)"}]; // Git配置ID
}
message ViewGitConfigurationResp{
  GitConfiguration configuration = 1;
}

message TestGitConfigurationReq{
  string url = 1 [(validate.rules).string = {uri: true, max_len: 255}]; // Git项目URL（http）
  string access_token = 2 [(validate.rules).string = {max_len: 128}]; // Git项目访问令牌
}
message TestGitConfigurationResp{
  repeated string branches = 1;
}

message SyncGitConfigurationReq{
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string config_id = 2 [(validate.rules).string = {pattern: "(?:^git_config_id:.+?)"}]; // Git配置ID
  common.TriggerMode trigger_mode = 3 [(validate.rules).enum = {in: [1, 3]}]; // 触发方式（手动触发、接口触发）
}
message SyncGitConfigurationResp{}

message SyncGitConfigurationByWebhookReq {
  string git_url = 1 [(validate.rules).string = {uri: true, max_len: 255}]; // Git项目URL（http）
  string target_branch = 2 [(validate.rules).string = {min_len: 1}]; // 目标分支
  string email = 3 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // 触发者邮箱
}
message SyncGitConfigurationByWebhookResp {}


//UiPlanService UI计划服务
service UiPlanService {
  //CreateUiPlan 创建UI计划
  rpc CreateUiPlan(CreateUiPlanReq) returns (CreateUiPlanResp);
  //RemoveUiPlan 删除UI计划
  rpc RemoveUiPlan(RemoveUiPlanReq) returns (RemoveUiPlanResp);
  //ModifyUiPlan 编辑UI计划
  rpc ModifyUiPlan(ModifyUiPlanReq) returns (ModifyUiPlanResp);
  //SearchUiPlan 搜索UI计划
  rpc SearchUiPlan(SearchUiPlanReq) returns (SearchUiPlanResp);
  //ViewUiPlan 查看UI计划
  rpc ViewUiPlan(ViewUiPlanReq)   returns (ViewUiPlanResp);
  //SearchUiPlanByProjectIdConfigId 搜索关联指定Git配置的UI计划
  rpc SearchUiPlanByProjectIdConfigId(SearchUiPlanByProjectIdConfigIdReq) returns (SearchUiPlanByProjectIdConfigIdResp);
  //GetCaseTreeOfUIPlan 获取UI计划的用例树
  rpc GetCaseTreeOfUIPlan(GetCaseTreeOfUIPlanReq) returns (GetCaseTreeOfUIPlanResp);
  //GetCaseTreeOfNotAddedToUIPlan 获取不在指定的UI计划中的用例树
  rpc GetCaseTreeOfNotAddedToUIPlan(GetCaseTreeOfNotAddedToUIPlanReq) returns (GetCaseTreeOfNotAddedToUIPlanResp);
  //SearchCaseInUIPlan 搜索UI计划中的用例
  rpc SearchCaseInUIPlan(SearchCaseInUIPlanReq) returns (SearchCaseInUIPlanResp);
  //ListDisableCaseInUIPlan 搜索UI计划中的失效用例
  rpc ListDisableCaseInUIPlan(ListDisableCaseInUIPlanReq) returns (ListDisableCaseInUIPlanResp);
  //SearchCaseNotInUIPlan 搜索不在指定的UI计划中的用例
  rpc SearchCaseNotInUIPlan(SearchCaseNotInUIPlanReq) returns (SearchCaseNotInUIPlanResp);
  //AddCaseToUIPlan 添加用例到UI计划中
  rpc AddCaseToUIPlan(AddCaseToUIPlanReq) returns (AddCaseToUIPlanResp);
  //RemoveCaseFromUIPlan 移除UI计划中的用例
  rpc RemoveCaseFromUIPlan(RemoveCaseFromUIPlanReq) returns (RemoveCaseFromUIPlanResp);
	//SearchLikeUiPlan 搜索收藏UI计划
	rpc SearchLikeUiPlan(SearchUiPlanReq) returns (SearchUiPlanResp);
}

message CreateUiPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 64}]; // UI计划名称
  string description = 3 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // UI计划描述
  common.TriggerMode type = 4 [(validate.rules).enum.defined_only = true]; // 计划类型
  string cron_expression = 5 [(validate.rules).string = {ignore_empty: true}]; // 定时触发的Cron表达式
  common.PriorityType priority_type = 6 [(validate.rules).enum.defined_only = true]; // 优先级
  string git_config_id = 7 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^git_config_id:.+?)"}]; // git配置ID
  ExecutionMode execution_mode = 8 [(validate.rules).enum = {in: [1, 2]}]; // 执行方式
  common.DeviceType device_type = 9 [(validate.rules).enum.defined_only = true]; // 设备类型（真机、云手机）
  common.PlatformType platform_type = 10 [(validate.rules).enum.defined_only = true]; // 平台类型（Android、iOS）
  string package_name = 11 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 包名，用于启动APP
  string callback_url = 12 [(validate.rules).string = {max_len: 255}]; // 回调地址
  string app_download_link = 13 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // APP下载地址
  string app_version = 14 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // APP版本
  string app_name = 15 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 应用名称
  common.TestLanguage test_language = 16 [(validate.rules).enum = {in: [1, 2]}]; // 测试语言
  string test_language_version = 17 [(validate.rules).string = {max_len: 50}]; // 测试语言版本
  common.TestFramework test_framework = 18 [(validate.rules).enum = {in: [1]}]; // 测试框架
  repeated string test_args = 19 [(validate.rules).repeated = {ignore_empty: true}]; // 附加参数
  string execution_environment = 20 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; //执行环境
  common.FailRetry  fail_retry = 21 [(validate.rules).enum.defined_only = true]; // 失败重试（0次、1次、2次）
  string maintained_by = 22 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 维护者
  repeated CasePathItem case_paths = 23 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 导入的用例路径列表
  string category_id = 24 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  repeated string devices = 25 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len: 1, max_len: 64}}}]; // 设备列表
  bool together = 26; // 选择的设备是否一起执行
}
message CreateUiPlanResp {
  UiPlan plan = 1;
}

message RemoveUiPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string plan_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^ui_plan_id:.+?)"}}}]; // 计划ID列表
}
message RemoveUiPlanResp {}

message ModifyUiPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^ui_plan_id:.+?)"}]; // UI计划ID
  string name = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // UI计划名称
  string description = 4 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // U计划描述
  common.TriggerMode type = 5 [(validate.rules).enum.defined_only = true]; // 计划类型
  string cron_expression = 6 [(validate.rules).string = {ignore_empty: true}]; // 定时触发的Cron表达式
  common.PriorityType priority_type = 7 [(validate.rules).enum.defined_only = true]; // 优先级
  string git_config_id = 8 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^git_config_id:.+?)"}]; // git配置ID
  ExecutionMode execution_mode = 9 [(validate.rules).enum = {in: [1, 2]}]; // 执行方式
  common.DeviceType device_type = 10 [(validate.rules).enum.defined_only = true]; // 设备类型（真机、云手机）
  common.PlatformType platform_type = 11 [(validate.rules).enum.defined_only = true]; // 平台类型（Android、iOS）
  string package_name = 12 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 包名，用于启动APP
  string callback_url = 13 [(validate.rules).string = {max_len: 255}]; // 回调地址
  string app_download_link = 14 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // APP下载地址
  string app_version = 15 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // APP版本
  string app_name = 16 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 应用名称
  common.TestLanguage test_language = 17 [(validate.rules).enum = {in: [1, 2]}]; // 测试语言
  string test_language_version = 18 [(validate.rules).string = {max_len: 50}]; // 测试语言版本
  common.TestFramework test_framework = 19 [(validate.rules).enum = {in: [1]}]; // 测试框架
  repeated string test_args = 20 [(validate.rules).repeated = {ignore_empty: true}]; // 附加参数
  string execution_environment = 21 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; //执行环境
  common.FailRetry  fail_retry = 22 [(validate.rules).enum.defined_only = true]; // 失败重试（0次、1次、2次）
  CommonState state = 23 [(validate.rules).enum = {in: [1, 2]}]; // UI计划状态
  string maintained_by = 24 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 维护者
  string category_id = 25 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  repeated string devices = 26 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len: 1, max_len: 64}}}]; // 设备列表
  bool together = 27; // 选择的设备是否一起执行
}
message ModifyUiPlanResp {
  UiPlan plan = 1;
}

message SearchUiPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  sqlbuilder.Condition condition = 2; // 查询条件
  sqlbuilder.Pagination pagination = 3; // 查询分页
  repeated sqlbuilder.SortField sort = 4 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
  string category_id = 5; // 分类ID
}
message SearchUiPlanResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated UiPlan items = 5;
}

message ViewUiPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^ui_plan_id:.+?)"}]; // 计划ID
}
message ViewUiPlanResp {
  UiPlan plan = 1;
}

message CheckPlanStateReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^ui_plan_id:.+?)"}]; // UI计划ID
}
message CheckPlanStateResp {
  int64 state = 1; // 生效为1，失效为2
}

message SearchUiPlanByProjectIdConfigIdReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string git_config_id = 2 [(validate.rules).string = {pattern: "(?:^git_config_id:.+?)"}]; // git配置ID
}
message SearchUiPlanByProjectIdConfigIdResp {
  repeated UiPlan items = 1;
}

message GetCaseTreeOfUIPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^ui_plan_id:.+?)"}]; // 计划ID
}
message GetCaseTreeOfUIPlanResp {
  repeated UICaseTreeNode case_tree = 1;
}

message GetCaseTreeOfNotAddedToUIPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^ui_plan_id:.+?)"}]; // 计划ID
}
message GetCaseTreeOfNotAddedToUIPlanResp {
  repeated UICaseTreeNode case_tree = 1;
}

message SearchCaseInUIPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^ui_plan_id:.+?)"}]; // 计划ID
  string path = 3 [(validate.rules).string = {ignore_empty: true}]; // 当前节点路径
  sqlbuilder.Condition condition = 4; // 查询条件
  sqlbuilder.Pagination pagination = 5; // 查询分页
  repeated sqlbuilder.SortField sort = 6 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchCaseInUIPlanResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated SearchCaseInUIPlanItem items = 5;
}

message ListDisableCaseInUIPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^ui_plan_id:.+?)"}]; // 计划ID
}
message ListDisableCaseInUIPlanResp {
  repeated SearchCaseInUIPlanItem items = 5;
}

message SearchCaseNotInUIPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^ui_plan_id:.+?)"}]; // 计划ID
  string path = 3 [(validate.rules).string = {ignore_empty: true}]; // 当前节点路径
  sqlbuilder.Condition condition = 4; // 查询条件
  sqlbuilder.Pagination pagination = 5; // 查询分页
  repeated sqlbuilder.SortField sort = 6 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchCaseNotInUIPlanResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated SearchCaseNotInUIPlanItem items = 5;
}

message AddCaseToUIPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^ui_plan_id:.+?)"}]; // 计划ID
  repeated string case_paths = 3 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len: 1}}}]; // 用例节点路径列表
}
message AddCaseToUIPlanResp {}

message RemoveCaseFromUIPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^ui_plan_id:.+?)"}]; // 计划ID
  repeated string case_paths = 3 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len: 1}}}]; // 用例节点路径列表
}
message RemoveCaseFromUIPlanResp {}


message HandleLikePlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2; // 计划ID
  bool is_like = 3; // 是否点赞/收藏
  common.PlanType plan_type = 4 [(validate.rules).enum.defined_only = true]; // 计划类型
}
message HandleLikePlanResp{}

message CheckLikePlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2; // 计划ID
  common.PlanType plan_type = 4 [(validate.rules).enum.defined_only = true]; // 计划类型
}
message CheckLikePlanResp{
  bool is_like = 3; // 是否点赞/收藏
}

//PlanCommonService 通用计划服务
service PlanCommonService {
	//GetApiExecutionData 处理计划收藏
	rpc HandleLikePlan(HandleLikePlanReq) returns (HandleLikePlanResp);
	//CheckLikePlan 检查计划收藏情况
	rpc CheckLikePlan(CheckLikePlanReq) returns ( CheckLikePlanResp);
}

//ReviewService 审核服务
service ReviewService {
  //CreateReviewRecord 申请审核
  rpc CreateReviewRecord(CreateReviewRecordReq) returns (CreateReviewRecordResp);
  //ModifyReviewRecord 编辑审核
  rpc ModifyReviewRecord(ModifyReviewRecordReq) returns (ModifyReviewRecordResp);
  //RevokeReviewRecord 撤回审核
  rpc RevokeReviewRecord(RevokeReviewRecordReq) returns (RevokeReviewRecordResp);
  //ApproveReviewRecord 审批审核
  rpc ApproveReviewRecord(ApproveReviewRecordReq) returns (ApproveReviewRecordResp);
  //SearchReviewRecord 搜索审核记录（包括申请记录）
  rpc SearchReviewRecord(SearchReviewRecordReq) returns (SearchReviewRecordResp);
  //Deprecated: SearchPendingReviewRecordByApplicant 搜索待审核的申请记录
  //rpc SearchPendingReviewRecordByApplicant(SearchPendingReviewRecordByApplicantReq) returns (SearchPendingReviewRecordByApplicantResp);
  //Deprecated: SearchCompletedReviewRecordByApplicant 搜索已完成的申请记录
  //rpc SearchCompletedReviewRecordByApplicant(SearchCompletedReviewRecordByApplicantReq) returns (SearchCompletedReviewRecordByApplicantResp);
  //Deprecated: SearchPendingReviewRecordByReviewer 搜索待审核的审核记录
  //rpc SearchPendingReviewRecordByReviewer(SearchPendingReviewRecordByReviewerReq) returns (SearchPendingReviewRecordByReviewerResp);
  //Deprecated: SearchCompletedReviewRecordByReviewer 搜索已完成的审核记录
  //rpc SearchCompletedReviewRecordByReviewer(SearchCompletedReviewRecordByReviewerReq) returns (SearchCompletedReviewRecordByReviewerResp);
}

message CreateReviewRecordReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  ReviewResourceType resource_type = 2 [(validate.rules).enum = {not_in: [0]}]; // 资源类型
  string resource_id = 3 [(validate.rules).string = {min_len: 4}]; // 资源ID
  string remark = 4 [(validate.rules).string = {ignore_empty: true, max_len: 1024}]; // 申请时的备注
  repeated string assigned_reviewers = 5 [(validate.rules).repeated = {min_items: 1, items: {string: {min_len: 1}}}]; // 指派的审核者
}
message CreateReviewRecordResp {
  ReviewRecord record = 1;
}

message ModifyReviewRecordReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string review_id = 2 [(validate.rules).string = {pattern: "(?:^review_id:.+?)"}]; // 审核ID
  string remark = 3 [(validate.rules).string = {ignore_empty: true, max_len: 1024}]; // 申请时的备注
  repeated string assigned_reviewers = 4; // 指派的审核者
}
message ModifyReviewRecordResp {
  ReviewRecord record = 1;
}

message RevokeReviewRecordReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string review_id = 2 [(validate.rules).string = {pattern: "(?:^review_id:.+?)"}]; // 审核ID
  string remark = 3 [(validate.rules).string = {ignore_empty: true, max_len: 1024}]; // 撤回时的备注
}
message RevokeReviewRecordResp {
  ReviewRecord record = 1;
}

message ApproveReviewRecordReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string review_id = 2 [(validate.rules).string = {pattern: "(?:^review_id:.+?)"}]; // 审核ID
  ReviewStatus result = 3 [(validate.rules).enum = {in: [3, 4]}]; // 审批结果
  string remark = 4 [(validate.rules).string = {ignore_empty: true, max_len: 1024}]; // 审批时的备注
}
message ApproveReviewRecordResp {
  ReviewRecord record = 1;
}

message SearchReviewRecordReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  sqlbuilder.Condition condition = 2; // 查询条件
  sqlbuilder.Pagination pagination = 3; // 查询分页
  repeated sqlbuilder.SortField sort = 4 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchReviewRecordResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated ReviewRecord items = 5;
}

//message SearchPendingReviewRecordByApplicantReq {
//  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
//  sqlbuilder.Condition condition = 2; // 查询条件
//  sqlbuilder.Pagination pagination = 3; // 查询分页
//  repeated sqlbuilder.SortField sort = 4 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
//}
//message SearchPendingReviewRecordByApplicantResp {
//  uint64 current_page = 1;
//  uint64 page_size = 2;
//  uint64 total_count = 3;
//  uint64 total_page = 4;
//  repeated ReviewRecord items = 5;
//}

//message SearchCompletedReviewRecordByApplicantReq {
//  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
//  sqlbuilder.Condition condition = 2; // 查询条件
//  sqlbuilder.Pagination pagination = 3; // 查询分页
//  repeated sqlbuilder.SortField sort = 4 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
//}
//message SearchCompletedReviewRecordByApplicantResp {
//  uint64 current_page = 1;
//  uint64 page_size = 2;
//  uint64 total_count = 3;
//  uint64 total_page = 4;
//  repeated ReviewRecord items = 5;
//}

//message SearchPendingReviewRecordByReviewerReq {
//  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
//  sqlbuilder.Condition condition = 2; // 查询条件
//  sqlbuilder.Pagination pagination = 3; // 查询分页
//  repeated sqlbuilder.SortField sort = 4 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
//}
//message SearchPendingReviewRecordByReviewerResp {
//  uint64 current_page = 1;
//  uint64 page_size = 2;
//  uint64 total_count = 3;
//  uint64 total_page = 4;
//  repeated ReviewRecord items = 5;
//}

//message SearchCompletedReviewRecordByReviewerReq {
//  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
//  sqlbuilder.Condition condition = 2; // 查询条件
//  sqlbuilder.Pagination pagination = 3; // 查询分页
//  repeated sqlbuilder.SortField sort = 4 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
//}
//message SearchCompletedReviewRecordByReviewerResp {
//  uint64 current_page = 1;
//  uint64 page_size = 2;
//  uint64 total_count = 3;
//  uint64 total_page = 4;
//  repeated ReviewRecord items = 5;
//}


//CaseCommonService 通用用例服务
service CaseCommonService {
	//SearchNotReleasedCase 搜索未上线用例
	rpc SearchNotReleasedCase(SearchCaseReq) returns (SearchCaseResp);
	//SearchFailLogCase 搜索用例失败记录
	rpc SearchFailLogCase(SearchCaseFailLogReq) returns (SearchCaseFailLogResp);
	//DeleteFailLogCase 删除（忽略）用例失败记录
	rpc DeleteFailLogCase(DeleteFailLogCaseReq) returns (DeleteFailLogCaseResp);
	//BatchDeleteFailLogCase 批量删除（忽略）用例失败记录
	rpc BatchDeleteFailLogCase(BatchDeleteCaseFailLogReq) returns (BatchDeleteCaseFailLogResp);
  //CreateOrModifyFailedCase 创建或修改失败用例记录
  rpc CreateOrModifyFailedCase(CreateOrModifyFailedCaseReq) returns (CreateOrModifyFailedCaseResp);
}

message SearchCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchCaseResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated Case items = 5;
}

message SearchCaseFailLogReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchCaseFailLogResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated FailCase items = 5;
}

message DeleteFailLogCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string case_type = 2 ; // 接口类型
  string case_id = 3 ; // 接口用例ID
}
message DeleteFailLogCaseResp {
}

message DeleteCaseFailLog {
  string case_type = 2 ; // 接口类型
  string case_id = 3 ; // 接口用例ID
}
message BatchDeleteCaseFailLogReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated DeleteCaseFailLog delete_case_fail_log_list = 2 [(validate.rules).repeated = {min_items: 1, items: {message: {required: true}}}];
}
message BatchDeleteCaseFailLogResp {
}

message CreateOrModifyFailedCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string case_type = 2 [(validate.rules).string = {in: ["API_CASE", "INTERFACE_CASE"]}]; // 用例类型
  string case_id = 3 [(validate.rules).string = {pattern: "(?:case|interface_case)_id:.+?"}]; // 用例ID
}
message CreateOrModifyFailedCaseResp {}


//ProtobufConfigurationService Protobuf配置服务
service ProtobufConfigurationService {
  //CreateProtobufConfiguration 创建Protobuf配置
  rpc CreateProtobufConfiguration(CreateProtobufConfigurationReq) returns (CreateProtobufConfigurationResp);
  //RemoveProtobufConfiguration 删除Protobuf配置
  rpc RemoveProtobufConfiguration(RemoveProtobufConfigurationReq) returns (RemoveProtobufConfigurationResp);
  //ModifyProtobufConfiguration 编辑Protobuf配置
  rpc ModifyProtobufConfiguration(ModifyProtobufConfigurationReq) returns (ModifyProtobufConfigurationResp);
  //SearchProtobufConfiguration 搜索Protobuf配置
  rpc SearchProtobufConfiguration(SearchProtobufConfigurationReq) returns (SearchProtobufConfigurationResp);
  //ViewProtobufConfiguration 查看Protobuf配置
  rpc ViewProtobufConfiguration(ViewProtobufConfigurationReq) returns (ViewProtobufConfigurationResp);
}

message CreateProtobufConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 通用配置名称
  string description = 3 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 通用配置描述
  string git_config_id = 4 [(validate.rules).string = {pattern: "(?:^git_config_id:.+?)"}]; // Git配置ID
  string import_path = 5 [(validate.rules).string = {min_len: 1, max_len: 255}]; // 导入路径（相对路径）
  repeated string exclude_paths = 6 [(validate.rules).repeated = {ignore_empty: true, unique: true, items: {string: {min_len: 1, max_len: 255}}}]; // 排除的路径（相对路径）
  repeated string exclude_files = 7 [(validate.rules).repeated = {ignore_empty: true, unique: true, items: {string: {min_len: 1, max_len: 255}}}]; // 排除的文件（相对路径）
  repeated string dependencies = 8 [(validate.rules).repeated = {ignore_empty: true, unique: true, items: {string: {pattern: "(?:^protobuf_config_id:.+?)"}}}]; // 依赖的Protobuf配置
}
message CreateProtobufConfigurationResp {
  ProtobufConfiguration configuration = 1;
}

message RemoveProtobufConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string config_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^protobuf_config_id:.+?)"}}}]; // Protobuf配置ID列表
}
message RemoveProtobufConfigurationResp {}

message ModifyProtobufConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string config_id = 2 [(validate.rules).string = {pattern: "(?:^protobuf_config_id:.+?)"}]; // Protobuf配置ID
  string name = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 通用配置名称
  string description = 4 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 通用配置描述
  string git_config_id = 5 [(validate.rules).string = {pattern: "(?:^git_config_id:.+?)"}]; // Git配置ID
  string import_path = 6 [(validate.rules).string = {min_len: 1, max_len: 255}]; // 导入路径（相对路径）
  repeated string exclude_paths = 7 [(validate.rules).repeated = {ignore_empty: true, unique: true, items: {string: {min_len: 1, max_len: 255}}}]; // 排除的路径（相对路径）
  repeated string exclude_files = 8 [(validate.rules).repeated = {ignore_empty: true, unique: true, items: {string: {min_len: 1, max_len: 255}}}]; // 排除的文件（相对路径）
  repeated string dependencies = 9 [(validate.rules).repeated = {ignore_empty: true, unique: true, items: {string: {pattern: "(?:^protobuf_config_id:.+?)"}}}]; // 依赖的Protobuf配置
}
message ModifyProtobufConfigurationResp {
  ProtobufConfiguration configuration = 1;
}

message SearchProtobufConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID

  sqlbuilder.Condition condition = 11; // 查询条件
  sqlbuilder.Pagination pagination = 12; // 查询分页
  repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchProtobufConfigurationResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated ProtobufConfiguration items = 5;
}

message ViewProtobufConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string config_id = 2 [(validate.rules).string = {pattern: "(?:^protobuf_config_id:.+?)"}]; // Protobuf配置ID
}
message ViewProtobufConfigurationResp {
  ProtobufConfiguration configuration = 1;
}


//PerfDataService 压测数据服务
service PerfDataService {
  //RemovePerfData 删除压测数据
  rpc RemovePerfData(RemovePerfDataReq) returns (RemovePerfDataResp);
  //SearchPerfData 搜索压测数据
  rpc SearchPerfData(SearchPerfDataReq) returns (SearchPerfDataResp);
}

message RemovePerfDataReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string data_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^perf_data_id:.+?)"}}}]; // 压测数据ID列表
}
message RemovePerfDataResp {}

message SearchPerfDataReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID

  sqlbuilder.Condition condition = 11; // 查询条件
  sqlbuilder.Pagination pagination = 12; // 查询分页
  repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchPerfDataResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated PerfData items = 5;
}


//PerfCaseService 压测用例服务
service PerfCaseService {
  //RemovePerfCase 删除压测用例
  rpc RemovePerfCase(RemovePerfCaseReq) returns (RemovePerfCaseResp);
  //ViewPerfCase 查看压测用例
  rpc ViewPerfCase(ViewPerfCaseReq) returns (ViewPerfCaseResp);
}

message RemovePerfCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string case_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^perf_case_id:.+?)"}}}]; // 压测用例ID列表
}
message RemovePerfCaseResp {}

message ViewPerfCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string case_id = 2 [(validate.rules).string = {pattern: "(?:^perf_case_id:.+?)"}]; // 压测用例ID
}
message ViewPerfCaseResp {
  PerfCase case = 1;
}


//PerfCaseV2Service 压测用例V2服务
service PerfCaseV2Service {
  //CreatePerfCaseV2 创建压测用例
  rpc CreatePerfCaseV2(CreatePerfCaseV2Req) returns (CreatePerfCaseV2Resp);
  //RemovePerfCaseV2 删除压测用例
  rpc RemovePerfCaseV2(RemovePerfCaseV2Req) returns (RemovePerfCaseV2Resp);
  //ModifyPerfCaseV2 编辑压测用例
  rpc ModifyPerfCaseV2(ModifyPerfCaseV2Req) returns (ModifyPerfCaseV2Resp);
  //SearchPerfCaseV2 搜索压测用例
  rpc SearchPerfCaseV2(SearchPerfCaseV2Req) returns (SearchPerfCaseV2Resp);
  //ViewPerfCaseV2 查看压测用例
  rpc ViewPerfCaseV2(ViewPerfCaseV2Req) returns (ViewPerfCaseV2Resp);
}

message CreatePerfCaseV2Req {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID

  string name = 11 [(validate.rules).string = {min_len: 1, max_len: 64}]; // API用例名称
  string description = 12 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // API用例描述
  repeated string tags = 13 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len:1, max_len: 64}}}]; // API用例标签
  common.Protocol protocol = 14 [(validate.rules).enum = {not_in: [0]}]; // 协议
  repeated common.RateLimitV2 rate_limits = 15 [(validate.rules).repeated = {min_items: 1, items: {message: {required: true}}}]; // 限流配置

  repeated common.PerfCaseStepV2 setup_steps = 21 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 前置步骤列表
  repeated common.PerfCaseStepV2 serial_steps = 22 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 串行步骤列表
  repeated common.PerfCaseStepV2 parallel_steps = 23 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 并行步骤列表
  repeated common.PerfCaseStepV2 teardown_steps = 24 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 后置步骤列表

  string maintained_by = 31 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 压测用例维护者
}
message CreatePerfCaseV2Resp {
  PerfCaseV2 case = 1;
}

message RemovePerfCaseV2Req {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string case_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^perf_case_id:.+?)"}}}]; // 压测用例ID列表
}
message RemovePerfCaseV2Resp {}

message ModifyPerfCaseV2Req {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  string case_id = 3 [(validate.rules).string = {pattern: "(?:^perf_case_id:.+?)"}]; // 压测用例ID

  string name = 11 [(validate.rules).string = {min_len: 1, max_len: 64}]; // API用例名称
  string description = 12 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // API用例描述
  repeated string tags = 13 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len:1, max_len: 64}}}]; // API用例标签
  common.Protocol protocol = 14 [(validate.rules).enum = {not_in: [0]}]; // 协议
  repeated common.RateLimitV2 rate_limits = 15 [(validate.rules).repeated = {min_items: 1, items: {message: {required: true}}}]; // 限流配置

  repeated common.PerfCaseStepV2 setup_steps = 21 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}];    // 前置步骤列表
  repeated common.PerfCaseStepV2 serial_steps = 22 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}];   // 串行步骤列表
  repeated common.PerfCaseStepV2 parallel_steps = 23 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 并行步骤列表
  repeated common.PerfCaseStepV2 teardown_steps = 24 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 后置步骤列表

  string maintained_by = 31 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 压测用例维护者
}
message ModifyPerfCaseV2Resp {
  PerfCaseV2 case = 1;
}

message SearchPerfCaseV2Req {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID

  sqlbuilder.Condition condition = 11; // 查询条件
  sqlbuilder.Pagination pagination = 12; // 查询分页
  repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchPerfCaseV2Resp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;

  repeated SearchPerfCaseV2Item items = 11;
}

message ViewPerfCaseV2Req {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string case_id = 2 [(validate.rules).string = {pattern: "(?:^perf_case_id:.+?)"}]; // 压测用例ID
}
message ViewPerfCaseV2Resp {
  PerfCaseV2 case = 1;
}


//PerfPlanService 压测计划服务
service PerfPlanService {
  //CreatePerfPlan 创建压测计划
  rpc CreatePerfPlan(CreatePerfPlanReq) returns (CreatePerfPlanResp);
  //RemovePerfPlan 删除压测计划
  rpc RemovePerfPlan(RemovePerfPlanReq) returns (RemovePerfPlanResp);
  //ModifyPerfPlan 编辑压测计划
  rpc ModifyPerfPlan(ModifyPerfPlanReq) returns (ModifyPerfPlanResp);
  //SearchPerfPlan 搜索压测计划
  rpc SearchPerfPlan(SearchPerfPlanReq) returns (SearchPerfPlanResp);
  //ViewPerfPlan 查看压测计划
  rpc ViewPerfPlan(ViewPerfPlanReq) returns (ViewPerfPlanResp);
  //SearchCaseInPerfPlan 搜索压测计划中的压测用例
  rpc SearchCaseInPerfPlan(SearchCaseInPerfPlanReq) returns (SearchCaseInPerfPlanResp);
}

message CreatePerfPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string name = 2 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 压测计划名称
  string description = 3 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 压测计划描述
  common.TriggerMode type = 4 [(validate.rules).enum = {in: [1, 2]}]; // 计划类型
  repeated string tags = 5 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len: 1, max_len: 64}}}]; // 标签
  common.Protocol protocol = 6 [(validate.rules).enum = {in: [2, 21]}]; // 协议
  string protobuf_config_id = 7 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^protobuf_config_id:.+?)", max_len: 64}]; // Protobuf配置ID
  string general_config_id = 8 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^general_config_id:.+?)", max_len: 64}]; // 通用配置ID
  string account_config_id = 9 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^account_config_id:.+?)", max_len: 64}]; // 池账号配置ID
  uint32 duration = 10 [(validate.rules).uint32 = {gt: 0, lte: 3600}]; // 压测持续时长
  common.TargetEnvironment target_env = 11 [(validate.rules).enum = {not_in: [0]}]; // 目标环境
  common.PerfKeepalive keepalive = 12; // 保活参数
  repeated string cases = 13 [(validate.rules).repeated = {min_items: 1, max_items: 10, unique: true, items: {string: {pattern: "(?:^perf_case_id:.+?)", max_len: 64}}}]; // 压测用例ID列表
  uint32 delay = 14 [(validate.rules).uint32 = {gte: 0, lte: 86400}]; // 延迟执行时间
  repeated common.LarkChat lark_chats = 15 [(validate.rules).repeated = {min_items: 1, items: {message: {required: true}}}]; // 飞书通知群组列表
  string maintained_by = 16 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 维护者
}
message CreatePerfPlanResp {
  PerfPlan plan = 1;
}

message RemovePerfPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string plan_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^perf_plan_id:.+?)"}}}]; // 计划ID列表
}
message RemovePerfPlanResp {}

message ModifyPerfPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^perf_plan_id:.+?)"}]; // 压测计划ID
  string name = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 压测计划名称
  string description = 4 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 压测计划描述
  common.TriggerMode type = 5 [(validate.rules).enum = {in: [1, 2]}]; // 计划类型
  repeated string tags = 6 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len: 1, max_len: 64}}}]; // 标签
  common.Protocol protocol = 7 [(validate.rules).enum = {in: [2, 21]}]; // 协议
  string protobuf_config_id = 8 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^protobuf_config_id:.+?)", max_len: 64}]; // Protobuf配置ID
  string general_config_id = 9 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^general_config_id:.+?)", max_len: 64}]; // 通用配置ID
  string account_config_id = 10 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^account_config_id:.+?)", max_len: 64}]; // 池账号配置ID
  uint32 duration = 11 [(validate.rules).uint32 = {gt: 0, lte: 3600}]; // 压测持续时长
  common.TargetEnvironment target_env = 12 [(validate.rules).enum = {not_in: [0]}]; // 目标环境
  common.PerfKeepalive keepalive = 13; // 保活参数
  repeated string cases = 14 [(validate.rules).repeated = {min_items: 1, max_items: 10, unique: true, items: {string: {pattern: "(?:^perf_case_id:.+?)", max_len: 64}}}]; // 压测用例ID列表
  uint32 delay = 15 [(validate.rules).uint32 = {gte: 0, lte: 86400}]; // 延迟执行时间
  repeated common.LarkChat lark_chats = 16 [(validate.rules).repeated = {min_items: 1, items: {message: {required: true}}}]; // 飞书通知群组列表
  string maintained_by = 17 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 维护者
}
message ModifyPerfPlanResp {
  PerfPlan plan = 1;
}

message SearchPerfPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID

  sqlbuilder.Condition condition = 11; // 查询条件
  sqlbuilder.Pagination pagination = 12; // 查询分页
  repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchPerfPlanResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated SearchPerfPlanItem items = 5;
}

message ViewPerfPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^perf_plan_id:.+?)"}]; // 计划ID
}
message ViewPerfPlanResp {
  PerfPlan plan = 1;
}

message SearchCaseInPerfPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^perf_plan_id:.+?)"}]; // 计划ID

  sqlbuilder.Condition condition = 11; // 查询条件
  sqlbuilder.Pagination pagination = 12; // 查询分页
  repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchCaseInPerfPlanResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated SearchCaseInPerfPlanItem items = 5;
}


//PerfPlanV2Service 压测计划V2服务
service PerfPlanV2Service {
  //CreatePerfPlanV2 创建压测计划
  rpc CreatePerfPlanV2(CreatePerfPlanV2Req) returns (CreatePerfPlanV2Resp);
  //RemovePerfPlanV2 删除压测计划
  rpc RemovePerfPlanV2(RemovePerfPlanV2Req) returns (RemovePerfPlanV2Resp);
  //ModifyPerfPlanV2 编辑压测计划
  rpc ModifyPerfPlanV2(ModifyPerfPlanV2Req) returns (ModifyPerfPlanV2Resp);
  //SearchPerfPlanV2 搜索压测计划
  rpc SearchPerfPlanV2(SearchPerfPlanV2Req) returns (SearchPerfPlanV2Resp);
  //ViewPerfPlanV2 查看压测计划
  rpc ViewPerfPlanV2(ViewPerfPlanV2Req) returns (ViewPerfPlanV2Resp);
  //SearchCaseInPerfPlanV2 搜索压测计划中的压测用例
  rpc SearchCaseInPerfPlanV2(SearchCaseInPerfPlanV2Req) returns (SearchCaseInPerfPlanV2Resp);
  //SearchProtobufInPerfPlanV2 搜索压测计划中的Protobuf配置
  rpc SearchProtobufInPerfPlanV2(SearchProtobufInPerfPlanV2Req) returns (SearchProtobufInPerfPlanV2Resp);
  //SearchRuleInPerfPlanV2 搜索压测计划中的停止规则
  rpc SearchRuleInPerfPlanV2(SearchRuleInPerfPlanV2Req) returns (SearchRuleInPerfPlanV2Resp);
  //UpdatePerfPlanByCase 通过编辑压测用例触发关联的压测计划进行相应的更新（包括：持续时长、压测数据、施压机资源）
  rpc UpdatePerfPlanByCase(UpdatePerfPlanByCaseReq) returns (UpdatePerfPlanByCaseResp);
  //UpdatePerfPlanByChatID 更新压测计划中自动创建的飞书群ID
  rpc UpdatePerfPlanByChatID(UpdatePerfPlanByChatIDReq) returns (UpdatePerfPlanByChatIDResp);
}

message CreatePerfPlanV2Req {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID

  string name = 11 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 压测计划名称
  string description = 12 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 压测计划描述
  common.TriggerMode type = 13 [(validate.rules).enum = {in: [1, 2]}]; // 计划类型
  string cron_expression = 14 [(validate.rules).string = {ignore_empty: true}]; // 定时触发的Cron表达式
  repeated string tags = 15 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len: 1, max_len: 64}}}]; // 标签
  common.Protocol protocol = 16 [(validate.rules).enum = {not_in: [0]}]; // 协议
  common.TargetEnvironment target_env = 17 [(validate.rules).enum = {not_in: [0]}]; // 目标环境
  repeated string protobuf_configs = 18 [(validate.rules).repeated = {ignore_empty: true, items: {string: {ignore_empty: true, pattern: "(?:^protobuf_config_id:.+?)", max_len: 64}}}]; // Protobuf配置ID列表
  string general_config_id = 19 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^general_config_id:.+?)", max_len: 64}]; // 通用配置ID
  string account_config_id = 20 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^account_config_id:.+?)", max_len: 64}]; // 池账号配置ID
  repeated common.RateLimitV2 auth_rate_limits = 21; // TT登录压测场景专用的登录接口限流配置

  bool custom_duration = 31; // 自定义压测持续时长
  uint32 duration = 32 [(validate.rules).uint32 = {gte: 0, lte: 3600}]; // 压测持续时长
  bool create_lark_chat = 33; // 是否需要自动拉群
  repeated common.LarkChat lark_chats = 34 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 飞书通知群组列表
  repeated string rules = 35 [(validate.rules).repeated = {ignore_empty: true, items: {string: {pattern: "(?:^perf_stop_rule_id:.+?)"}}}]; // 压测停止规则列表
  bool advanced_notification = 36; // 是否需要提前通知

  repeated PerfPlanCaseV2Item cases = 41 [(validate.rules).repeated = {min_items: 1, max_items: 20, items: {message: {required: true}}}]; // 压测用例列表

  string maintained_by = 51 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 维护者
}
message CreatePerfPlanV2Resp {
  PerfPlanV2 plan = 1;
}

message RemovePerfPlanV2Req {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string plan_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^perf_plan_id:.+?)"}}}]; // 计划ID列表
}
message RemovePerfPlanV2Resp {}

message ModifyPerfPlanV2Req {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^perf_plan_id:.+?)"}]; // 压测计划ID
  string category_id = 3 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID

  string name = 11 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 压测计划名称
  string description = 12 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 压测计划描述
  common.TriggerMode type = 13 [(validate.rules).enum = {in: [1, 2]}]; // 计划类型
  string cron_expression = 14 [(validate.rules).string = {ignore_empty: true}]; // 定时触发的Cron表达式
  repeated string tags = 15 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len: 1, max_len: 64}}}]; // 标签
  common.Protocol protocol = 16 [(validate.rules).enum = {not_in: [0]}]; // 协议
  common.TargetEnvironment target_env = 17 [(validate.rules).enum = {not_in: [0]}]; // 目标环境
  repeated string protobuf_configs = 18 [(validate.rules).repeated = {ignore_empty: true, items: {string: {ignore_empty: true, pattern: "(?:^protobuf_config_id:.+?)", max_len: 64}}}]; // Protobuf配置ID列表
  string general_config_id = 19 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^general_config_id:.+?)", max_len: 64}]; // 通用配置ID
  string account_config_id = 20 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^account_config_id:.+?)", max_len: 64}]; // 池账号配置ID
  repeated common.RateLimitV2 auth_rate_limits = 21; // TT登录压测场景专用的登录接口限流配置

  bool custom_duration = 31; // 自定义压测持续时长
  uint32 duration = 32 [(validate.rules).uint32 = {gte: 0, lte: 3600}]; // 压测持续时长
  bool create_lark_chat = 33; // 是否需要自动拉群
  repeated common.LarkChat lark_chats = 34 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 飞书通知群组列表
  repeated string rules = 35 [(validate.rules).repeated = {ignore_empty: true, items: {string: {pattern: "(?:^perf_stop_rule_id:.+?)"}}}]; // 压测停止规则列表
  bool advanced_notification = 36; // 是否需要提前通知

  repeated PerfPlanCaseV2Item cases = 41 [(validate.rules).repeated = {min_items: 1, max_items: 20, items: {message: {required: true}}}]; // 压测用例列表

  CommonState state = 51 [(validate.rules).enum = {in: [1, 2]}]; // 压测计划状态
  string maintained_by = 52 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 维护者
}
message ModifyPerfPlanV2Resp {
  PerfPlanV2 plan = 1;
}

message SearchPerfPlanV2Req {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2; // 分类ID
  
  sqlbuilder.Condition condition = 11; // 查询条件
  sqlbuilder.Pagination pagination = 12; // 查询分页
  repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchPerfPlanV2Resp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;

  repeated SearchPerfPlanV2Item items = 11;
}

message ViewPerfPlanV2Req {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^perf_plan_id:.+?)"}]; // 计划ID
}
message ViewPerfPlanV2Resp {
  PerfPlanV2 plan = 1;
}

message SearchCaseInPerfPlanV2Req {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^perf_plan_id:.+?)"}]; // 计划ID

  sqlbuilder.Condition condition = 11; // 查询条件
  sqlbuilder.Pagination pagination = 12; // 查询分页
  repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchCaseInPerfPlanV2Resp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;

  repeated SearchCaseInPerfPlanV2Item items = 11;
}

message SearchProtobufInPerfPlanV2Req {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^perf_plan_id:.+?)"}]; // 计划ID

  sqlbuilder.Condition condition = 11; // 查询条件
  sqlbuilder.Pagination pagination = 12; // 查询分页
  repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchProtobufInPerfPlanV2Resp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;

  repeated SearchProtobufInPerfPlanV2Item items = 11;
}

message SearchRuleInPerfPlanV2Req {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^perf_plan_id:.+?)"}]; // 计划ID

  sqlbuilder.Condition condition = 11; // 查询条件
  sqlbuilder.Pagination pagination = 12; // 查询分页
  repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchRuleInPerfPlanV2Resp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;

  repeated SearchRuleInPerfPlanV2Item items = 11;
}

message UpdatePerfPlanByCaseReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^perf_plan_id:.+?)"}]; // 压测计划ID
  string case_id = 3 [(validate.rules).string = {pattern: "(?:^perf_case_id:.+?)"}]; // 压测用例ID
}
message UpdatePerfPlanByCaseResp {}

message UpdatePerfPlanByChatIDReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^perf_plan_id:.+?)"}]; // 压测计划ID
  string chat_id = 3 [(validate.rules).string = {pattern: "^oc_[a-z0-9]{32}$"}]; // 飞书群组ID
}
message UpdatePerfPlanByChatIDResp {}


//PerfStopRuleService 压测停止规则服务
service PerfStopRuleService {
  //CreatePerfStopRule 创建压测停止规则
  rpc CreatePerfStopRule(CreatePerfStopRuleReq) returns (CreatePerfStopRuleResp);
  //RemovePerfStopRule 删除压测停止规则
  rpc RemovePerfStopRule(RemovePerfStopRuleReq) returns (RemovePerfStopRuleResp);
  //ModifyPerfStopRule 编辑压测停止规则
  rpc ModifyPerfStopRule(ModifyPerfStopRuleReq) returns (ModifyPerfStopRuleResp);
  //SearchPerfStopRule 搜索压测停止规则
  rpc SearchPerfStopRule(SearchPerfStopRuleReq) returns (SearchPerfStopRuleResp);
  //ViewPerfStopRule 查看压测停止规则
  rpc ViewPerfStopRule(ViewPerfStopRuleReq) returns (ViewPerfStopRuleResp);
}

message CreatePerfStopRuleReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID

  string name = 11 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 压测停止规则名称
  string description = 12 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 压测停止规则描述
  common.MetricType metric_type = 13 [(validate.rules).enum = {not_in: [0]}]; // 指标类型
  double threshold = 14 [(validate.rules).double = {gte: 0}]; // 阀值
  uint32 duration = 15 [(validate.rules).uint32 = {gte: 60, lte: 3600}]; // 持续时间
}
message CreatePerfStopRuleResp {
  PerfStopRule rule = 1;
}

message RemovePerfStopRuleReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string rule_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^perf_stop_rule_id:.+?)"}}}]; // 规则ID列表
}
message RemovePerfStopRuleResp {}

message ModifyPerfStopRuleReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string rule_id = 2 [(validate.rules).string = {pattern: "(?:^perf_stop_rule_id:.+?)"}]; // 压测停止规则ID

  string name = 11 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 压测停止规则名称
  string description = 12 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 压测停止规则描述
  common.MetricType metric_type = 13 [(validate.rules).enum = {not_in: [0]}]; // 指标类型
  double threshold = 14 [(validate.rules).double = {gte: 0}]; // 阀值
  uint32 duration = 15 [(validate.rules).uint32 = {gte: 60, lte: 3600}]; // 持续时间

  CommonState state = 21 [(validate.rules).enum = {in: [1, 2]}]; // 压测停止规则状态
}
message ModifyPerfStopRuleResp {
  PerfStopRule rule = 1;
}

message SearchPerfStopRuleReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID

  sqlbuilder.Condition condition = 11; // 查询条件
  sqlbuilder.Pagination pagination = 12; // 查询分页
  repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchPerfStopRuleResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;

  repeated PerfStopRule items = 11;
}

message ViewPerfStopRuleReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string rule_id = 2 [(validate.rules).string = {pattern: "(?:^perf_stop_rule_id:.+?)"}]; // 压测停止规则ID
}
message ViewPerfStopRuleResp {
  PerfStopRule rule = 1;
}


//PerfLarkChatService 压测通知飞书群组服务
service PerfLarkChatService {
  //ModifyPerfLarkChat 编辑压测通知飞书群组列表
  rpc ModifyPerfLarkChat(ModifyPerfLarkChatReq) returns (ModifyPerfLarkChatResp);
  //SearchPerfLarkChat 搜索压测通知飞书群组
  rpc SearchPerfLarkChat(SearchPerfLarkChatReq) returns (SearchPerfLarkChatResp);

  //DeletePerfLarkChat 删除压测通知飞书群组（由飞书群解散事件触发）
  rpc DeletePerfLarkChat(DeletePerfLarkChatReq) returns (DeletePerfLarkChatResp);
  //UpdatePerfLarkChat 更新压测通知飞书群组（由飞书群配置修改事件触发）
  rpc UpdatePerfLarkChat(UpdatePerfLarkChatReq) returns (UpdatePerfLarkChatResp);
}

message ModifyPerfLarkChatReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string lark_chats = 2 [(validate.rules).repeated = {ignore_empty: true, items: {string: {len: 35, pattern: "oc_[0-9a-z]{32}"}}}]; // 飞书群组列表
}
message ModifyPerfLarkChatResp {}

message SearchPerfLarkChatReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID

  sqlbuilder.Condition condition = 11; // 查询条件
  sqlbuilder.Pagination pagination = 12; // 查询分页
  repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchPerfLarkChatResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;

  repeated PerfLarkChat items = 11;
}

message DeletePerfLarkChatReq {
  string chat_id = 1 [(validate.rules).string = {len: 35, pattern: "oc_[0-9a-z]{32}"}]; // 飞书群组ID
}
message DeletePerfLarkChatResp {}

message UpdatePerfLarkChatReq {
  string chat_id = 1 [(validate.rules).string = {len: 35, pattern: "oc_[0-9a-z]{32}"}]; // 飞书群组ID
  string name = 2 [(validate.rules).string = {min_len: 2, max_len: 60}]; // 飞书群组名称
  string avatar = 3 [(validate.rules).string = {uri: true}]; // 飞书群组头像URL
  string description = 4 [(validate.rules).string = {max_len: 100}]; // 飞书群组描述
  bool external = 5; // 是否是外部群
}
message UpdatePerfLarkChatResp {}

//PerfLarkMemberService 压测飞书自动拉群成员服务
service PerfLarkMemberService {
  //CreatePerfLarkMember 添加飞书自动拉群成员
  rpc CreatePerfLarkMember(CreatePerfLarkMemberReq) returns (CreatePerfLarkMemberResp);
  //RemovePerfLarkMember 删除飞书自动拉群成员
  rpc RemovePerfLarkMember(RemovePerfLarkMemberReq) returns (RemovePerfLarkMemberResp);
  //SearchPerfLarkMember 搜索飞书自动拉群成员
  rpc SearchPerfLarkMember(SearchPerfLarkMemberReq) returns (SearchPerfLarkMemberResp);
}

message CreatePerfLarkMemberReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}];
  repeated user.UserInfo items = 2;
}
message CreatePerfLarkMemberResp {
}

message RemovePerfLarkMemberReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}];
  string account = 2;
}
message RemovePerfLarkMemberResp {
}

message SearchPerfLarkMemberReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  sqlbuilder.Condition condition = 2; // 查询条件
  sqlbuilder.Pagination pagination = 3; // 查询分页
  repeated sqlbuilder.SortField sort = 4 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchPerfLarkMemberResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated user.UserInfo items = 5;
}


//ProjectDeviceService 项目设备服务
service ProjectDeviceService {
  //ModifyProjectDevice 编辑项目设备列表
  rpc ModifyProjectDevice(ModifyProjectDeviceReq) returns (ModifyProjectDeviceResp);
  //GetProjectDevice 获取项目设备
  rpc GetProjectDevice(GetProjectDeviceReq) returns (GetProjectDeviceResp);
  //SearchProjectDevice 搜索项目设备
  rpc SearchProjectDevice(SearchProjectDeviceReq) returns (SearchProjectDeviceResp);
  //SearchUnassignedProjectDevice 搜索未分配到项目的设备
  rpc SearchUnassignedProjectDevice(SearchUnassignedProjectDeviceReq) returns (SearchUnassignedProjectDeviceResp);
  //SearchProjectDeviceReference 搜索项目设备引用详情
  rpc SearchProjectDeviceReference(SearchProjectDeviceReferenceReq) returns (SearchProjectDeviceReferenceResp);

  //Deprecated: use `ProjectDeviceService.AcquireProjectDeviceByCondition` instead. AcquireProjectDevice 占用项目设备
  rpc AcquireProjectDevice(AcquireProjectDeviceReq) returns (AcquireProjectDeviceResp);
  //AcquireProjectDeviceByUDID 占用项目设备（通过`udid`）
  rpc AcquireProjectDeviceByUDID(AcquireProjectDeviceByUDIDReq) returns (AcquireProjectDeviceByUDIDResp);
  //AcquireProjectDeviceByCondition 占用项目设备（通过查询条件）
  rpc AcquireProjectDeviceByCondition(AcquireProjectDeviceByConditionReq) returns (AcquireProjectDeviceByConditionResp);
  //ReleaseProjectDevice 释放项目设备
  rpc ReleaseProjectDevice(ReleaseProjectDeviceReq) returns (ReleaseProjectDeviceResp);

  //DeleteDisabledProjectDevice 删除无效（设备中心不存在）的项目设备
  rpc DeleteDisabledProjectDevice(DeleteDisabledProjectDeviceReq) returns (DeleteDisabledProjectDeviceResp);
}

message ModifyProjectDeviceReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated BindDevice devices = 2 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 设备列表（全量提交）
}
message ModifyProjectDeviceResp {
  repeated BindDevice create_items = 1;                // 新增的设备
  repeated BindDevice update_items = 2;                // 更新的设备
  repeated BindDevice delete_items = 3;                // 删除的设备
  repeated BindDevice ignore_create_items = 4;         // 忽略新增的设备（设备不存在则不能被新增，忽略该设备）
  repeated DeviceRelationship ignore_update_items = 5; // 忽略更新的设备（被计划引用的设备不能更新用途，忽略该设备）
  repeated DeviceRelationship ignore_delete_items = 6; // 忽略删除的设备（被计划引用的设备不能删除，忽略该设备）
}

message GetProjectDeviceReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  common.DeviceUsage usage = 2 [(validate.rules).enum = {defined_only: true}]; // 设备用途
  string udid = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 设备编号
}
message GetProjectDeviceResp {
  ProjectDevice device = 1;
}

message SearchProjectDeviceReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  common.DeviceUsage usage = 2 [(validate.rules).enum = {defined_only: true}]; // 设备用途

  sqlbuilder.Condition condition = 11; // 查询条件
  sqlbuilder.Pagination pagination = 12; // 查询分页
  repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 查询排序
}
message SearchProjectDeviceResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;

  repeated ProjectDevice items = 11;
}

message SearchUnassignedProjectDeviceReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID

  sqlbuilder.Condition condition = 11; // 查询条件
  sqlbuilder.Pagination pagination = 12; // 查询分页
  repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 查询排序
}
message SearchUnassignedProjectDeviceResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;

  repeated ProjectDevice items = 11;
}

message SearchProjectDeviceReferenceReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string udid = 2 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 设备编号

  sqlbuilder.Condition condition = 11; // 查询条件
  sqlbuilder.Pagination pagination = 12; // 查询分页
  repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 查询排序
}
message SearchProjectDeviceReferenceResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  
  repeated SearchProjectDeviceReferenceItem items = 11;
}

message AcquireProjectDeviceReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  common.DeviceUsage usage = 2 [(validate.rules).enum = {not_in: [0]}]; // 设备用途
  sqlbuilder.Condition condition = 3; // 查询条件
  uint32 count = 4 [(validate.rules).uint32.gte = 1]; // 申请占用的设备数量
  int64 expiration = 5 [(validate.rules).int64.gte = 0]; // 最大占用时长（秒）
}
message AcquireProjectDeviceResp {
  repeated ProjectDevice devices = 1;
}

message AcquireProjectDeviceByUDIDReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  common.DeviceUsage usage = 2 [(validate.rules).enum = {not_in: [0]}]; // 设备用途
  string udid = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 设备编号
  int64 expiration = 4 [(validate.rules).int64.gte = 0]; // 最大占用时长（秒）
}
message AcquireProjectDeviceByUDIDResp {
  ProjectDevice device = 1;
}

message AcquireProjectDeviceByConditionReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  common.DeviceUsage usage = 2 [(validate.rules).enum = {not_in: [0]}]; // 设备用途
  sqlbuilder.Condition condition = 3; // 查询条件
  uint32 count = 4 [(validate.rules).uint32.gte = 1]; // 申请占用的设备数量
  int64 expiration = 5 [(validate.rules).int64.gte = 0]; // 最大占用时长（秒）
}
message AcquireProjectDeviceByConditionResp {
  repeated ProjectDevice devices = 1;
}

message ReleaseProjectDeviceReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  common.DeviceUsage usage = 2 [(validate.rules).enum = {not_in: [0]}]; // 设备用途
  string udid = 3 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 设备编号
  string token = 4 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 设备令牌
}
message ReleaseProjectDeviceResp {}

message DeleteDisabledProjectDeviceReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
}
message DeleteDisabledProjectDeviceResp {}

//StabilityPlanService 稳定性测试计划服务
service StabilityPlanService {
  //CreateStabilityPlan 创建稳定性测试计划
  rpc CreateStabilityPlan(CreateStabilityPlanReq) returns (CreateStabilityPlanResp);
  //RemoveStabilityPlan 删除稳定性测试计划
  rpc RemoveStabilityPlan(RemoveStabilityPlanReq) returns (RemoveStabilityPlanResp);
  //ModifyStabilityPlan 编辑稳定性测试计划
  rpc ModifyStabilityPlan(ModifyStabilityPlanReq) returns (ModifyStabilityPlanResp);
  //SearchStabilityPlan 搜索稳定性测试计划
  rpc SearchStabilityPlan(SearchStabilityPlanReq) returns (SearchStabilityPlanResp);
  //ViewStabilityPlan   查看稳定性测试计划
  rpc ViewStabilityPlan(ViewStabilityPlanReq) returns (ViewStabilityPlanResp);
}

message CreateStabilityPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  
  string name = 11 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 计划名称
  string description = 12 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 描述
  CommonState state = 13 [(validate.rules).enum = {in: [1, 2]}]; // 状态
  common.TriggerMode type = 14 [(validate.rules).enum = {in: [1, 2]}]; // 计划类型（手动、定时）
  common.PriorityType priority_type = 15 [(validate.rules).enum.defined_only = true]; // 优先级
  string cron_expression = 16 [(validate.rules).string = {ignore_empty: true}]; // 定时触发计划的Cron表达式
  repeated string tags = 17 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len: 1, max_len: 64}}}]; // 标签

  string account_config_id = 31 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^account_config_id:.+?)", max_len: 64}]; // 池账号配置ID
  common.DeviceType device_type = 32 [(validate.rules).enum.defined_only = true]; // 设备类型（真机、云手机）
  common.PlatformType platform_type = 33 [(validate.rules).enum.defined_only = true]; // 平台类型（Android、iOS）
  common.StabilityCustomDevices devices = 34; // 自定义设备
  string package_name = 35 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 包名，用于启动APP
  string app_download_link = 36 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // APP下载地址
  uint32 duration = 37 [(validate.rules).uint32 = {gte: 0, lte: 3600}]; // 运行时长（单位：分钟）
  repeated string activities = 38 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len: 1, max_len: 255}}}]; // 启动的Activity
  common.StabilityCustomScript custom_script = 39; // 自定义脚本
  repeated common.LarkChat lark_chats = 40 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 飞书通知群组列表
                                        
  string maintained_by = 51 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 维护者
}
message CreateStabilityPlanResp {
  StabilityPlan plan = 1;
}

message RemoveStabilityPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string plan_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^stability_plan_id:.+?)"}}}]; // 计划ID列表
}
message RemoveStabilityPlanResp {
}

message ModifyStabilityPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  string plan_id = 3 [(validate.rules).string = {pattern: "(?:^stability_plan_id:.+?)"}]; // 计划ID
  
  string name = 11 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 计划名称
  string description = 12 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 描述
  CommonState state = 13 [(validate.rules).enum = {in: [1, 2]}]; // 状态
  common.TriggerMode type = 14 [(validate.rules).enum = {in: [1, 2]}]; // 计划类型（手动、定时）
  common.PriorityType priority_type = 15 [(validate.rules).enum.defined_only = true]; // 优先级
  string cron_expression = 16 [(validate.rules).string = {ignore_empty: true}]; // 定时触发计划的Cron表达式
  repeated string tags = 17 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len: 1, max_len: 64}}}]; // 标签

  string account_config_id = 31 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^account_config_id:.+?)", max_len: 64}]; // 池账号配置ID
  common.DeviceType device_type = 32 [(validate.rules).enum.defined_only = true]; // 设备类型（真机、云手机）
  common.PlatformType platform_type = 33 [(validate.rules).enum.defined_only = true]; // 平台类型（Android、iOS）
  common.StabilityCustomDevices devices = 34; // 自定义设备
  string package_name = 35 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 包名，用于启动APP
  string app_download_link = 36 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // APP下载地址
  uint32 duration = 37 [(validate.rules).uint32 = {gte: 0, lte: 3600}]; // 运行时长（单位：分钟）
  repeated string activities = 38 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len: 1, max_len: 255}}}]; // 启动的Activity
  common.StabilityCustomScript custom_script = 39; // 自定义脚本
  repeated common.LarkChat lark_chats = 40 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 飞书通知群组列表
                                        
  string maintained_by = 51 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 维护者
}
message ModifyStabilityPlanResp {
  StabilityPlan plan = 1;
}

message SearchStabilityPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2; // 分类ID
  sqlbuilder.Condition condition = 3; // 查询条件
  sqlbuilder.Pagination pagination = 4; // 查询分页
  repeated sqlbuilder.SortField sort = 5 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
  
}
message SearchStabilityPlanResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated StabilityPlan items = 5;
}

message ViewStabilityPlanReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^stability_plan_id:.+?)"}]; // 计划ID
}
message ViewStabilityPlanResp {
  StabilityPlan plan = 1;
}

//LarkChatService 测试通知飞书群组服务
service LarkChatService {
  //ModifyLarkChat 编辑测试通知飞书群组列表
  rpc ModifyLarkChat(ModifyLarkChatReq) returns (ModifyLarkChatResp);
  //SearchLarkChat 搜索测试通知飞书群组
  rpc SearchLarkChat(SearchLarkChatReq) returns (SearchLarkChatResp);

  //DeleteLarkChat 删除测试通知飞书群组（由飞书群解散事件触发）
  rpc DeleteLarkChat(DeleteLarkChatReq) returns (DeleteLarkChatResp);
  //UpdateLarkChat 更新测试通知飞书群组（由飞书群配置修改事件触发）
  rpc UpdateLarkChat(UpdateLarkChatReq) returns (UpdateLarkChatResp);
}

message ModifyLarkChatReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string lark_chats = 2 [(validate.rules).repeated = {ignore_empty: true, items: {string: {len: 35, pattern: "oc_[0-9a-z]{32}"}}}]; // 飞书群组列表
  string type = 3 [(validate.rules).string = {in: ["PERF", "STABILITY"]}]; // 测试类型
}
message ModifyLarkChatResp {}

message SearchLarkChatReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string type = 2 [(validate.rules).string = {in: ["PERF", "STABILITY"]}]; // 测试类型

  sqlbuilder.Condition condition = 11; // 查询条件
  sqlbuilder.Pagination pagination = 12; // 查询分页
  repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchLarkChatResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;

  repeated LarkChat items = 11;
}

message DeleteLarkChatReq {
  string chat_id = 1 [(validate.rules).string = {len: 35, pattern: "oc_[0-9a-z]{32}"}]; // 飞书群组ID
}
message DeleteLarkChatResp {}

message UpdateLarkChatReq {
  string chat_id = 1 [(validate.rules).string = {len: 35, pattern: "oc_[0-9a-z]{32}"}]; // 飞书群组ID
  string name = 2 [(validate.rules).string = {min_len: 2, max_len: 60}]; // 飞书群组名称
  string avatar = 3 [(validate.rules).string = {uri: true}]; // 飞书群组头像URL
  string description = 4 [(validate.rules).string = {max_len: 100}]; // 飞书群组描述
  bool external = 5; // 是否是外部群
}
message UpdateLarkChatResp {}

//SlaThresholdService SLA阈值服务
service SlaThresholdService {
  //ModifySlaThreshold( 编辑SLA阈值配置
  rpc ModifySlaThreshold(ModifySlaThresholdReq) returns (ModifySlaThresholdResp);
  //GetSlaThreshold 获取SLA阈值配置
  rpc GetSlaThreshold(GetSlaThresholdReq) returns (GetSlaThresholdResp);
}

message ModifySlaThresholdReq {
  repeated SlaThreshold thresholds = 1;
}
message ModifySlaThresholdResp {}

message GetSlaThresholdReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
}
message GetSlaThresholdResp {
  repeated SlaThreshold thresholds = 1;
}

//SlaNotifierService SLA通知人员服务
service SlaNotifierService {
  //SearchSlaNotifier 获取SLA通知人员
  rpc SearchSlaNotifier(SearchSlaNotifierReq) returns (SearchSlaNotifierResp);
}

message SearchSlaNotifierReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  sqlbuilder.Condition condition = 2; // 查询条件
  sqlbuilder.Pagination pagination = 3; // 查询分页
  repeated sqlbuilder.SortField sort = 4 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchSlaNotifierResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;
  repeated user.UserInfo items = 5;
}


//PromptConfigurationService Prompt配置服务
service PromptConfigurationService {
  //CreatePromptConfiguration 创建Prompt配置
  rpc CreatePromptConfiguration(CreatePromptConfigurationReq) returns (CreatePromptConfigurationResp);
  //RemovePromptConfiguration 删除Prompt配置
  rpc RemovePromptConfiguration(RemovePromptConfigurationReq) returns (RemovePromptConfigurationResp);
  //ModifyPromptConfiguration 编辑Prompt配置
  rpc ModifyPromptConfiguration(ModifyPromptConfigurationReq) returns (ModifyPromptConfigurationResp);
  //SearchPromptConfiguration 搜索Prompt配置
  rpc SearchPromptConfiguration(SearchPromptConfigurationReq) returns (SearchPromptConfigurationResp);
  //ViewPromptConfiguration 查看Prompt配置
  rpc ViewPromptConfiguration(ViewPromptConfigurationReq) returns (ViewPromptConfigurationResp);
  rpc SearchPromptConfigurationReference(SearchPromptConfigurationReferenceReq) returns (SearchPromptConfigurationReferenceResp);
}

message CreatePromptConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  common.PromptPurpose purpose = 2 [(validate.rules).enum = {in: [1]}]; // 用途（1: UI_AGENT）
  common.PromptCategory category = 3 [(validate.rules).enum = {not_in: [0]}]; // 分类（1: 背景、2: UI组件、3: 异常处理）

  string name = 11 [(validate.rules).string = {min_len: 1, max_len: 64}]; // Prompt配置名称
  string description = 12 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // Prompt配置描述
  string content = 13 [(validate.rules).string = {min_len: 1}]; // Prompt配置内容
}
message CreatePromptConfigurationResp {
  PromptConfiguration configuration = 1;
}

message RemovePromptConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string config_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^prompt_config_id:.+?)"}}}]; // Prompt配置ID列表
}
message RemovePromptConfigurationResp {}

message ModifyPromptConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string config_id = 2 [(validate.rules).string = {pattern: "(?:^prompt_config_id:.+?)"}]; // Prompt配置ID

  string name = 11 [(validate.rules).string = {min_len: 1, max_len: 64}]; // Prompt配置名称
  string description = 12 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // Prompt配置描述
  string content = 13 [(validate.rules).string = {min_len: 1}]; // Prompt配置内容
}
message ModifyPromptConfigurationResp {
  PromptConfiguration configuration = 1;
}

message SearchPromptConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  common.PromptPurpose purpose = 2 [(validate.rules).enum = {in: [1]}]; // 用途

  sqlbuilder.Condition condition = 11; // 查询条件
  sqlbuilder.Pagination pagination = 12; // 查询分页
  repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchPromptConfigurationResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;

  repeated PromptConfiguration items = 11;
}

message ViewPromptConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string config_id = 2 [(validate.rules).string = {pattern: "(?:^prompt_config_id:.+?)"}]; // Prompt配置ID
}
message ViewPromptConfigurationResp {
  PromptConfiguration configuration = 1;
}

message SearchPromptConfigurationReferenceReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string config_id = 2 [(validate.rules).string = {pattern: "(?:^prompt_config_id:.+?)"}]; // Prompt配置ID

  sqlbuilder.Condition condition = 11; // 查询条件
  sqlbuilder.Pagination pagination = 12; // 查询分页
  repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchPromptConfigurationReferenceResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;

  repeated SearchPromptConfigurationReferenceItem items = 11;
}


//ApplicationConfigurationService 应用配置服务
service ApplicationConfigurationService {
  //CreateApplicationConfiguration 创建应用配置
  rpc CreateApplicationConfiguration(CreateApplicationConfigurationReq) returns (CreateApplicationConfigurationResp);
  //RemoveApplicationConfiguration 删除应用配置
  rpc RemoveApplicationConfiguration(RemoveApplicationConfigurationReq) returns (RemoveApplicationConfigurationResp);
  //ModifyApplicationConfiguration 编辑应用配置
  rpc ModifyApplicationConfiguration(ModifyApplicationConfigurationReq) returns (ModifyApplicationConfigurationResp);
  //SearchApplicationConfiguration 搜索应用配置
  rpc SearchApplicationConfiguration(SearchApplicationConfigurationReq) returns (SearchApplicationConfigurationResp);
  //ViewApplicationConfiguration 查看应用配置
  rpc ViewApplicationConfiguration(ViewApplicationConfigurationReq) returns (ViewApplicationConfigurationResp);
  //SearchApplicationConfigurationReference 搜索应用配置引用详情
  rpc SearchApplicationConfigurationReference(SearchApplicationConfigurationReferenceReq) returns (SearchApplicationConfigurationReferenceResp);
}

message CreateApplicationConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID

  string name = 11 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 应用配置名称
  string description = 12 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 应用配置描述
  common.PlatformType platform_type = 13 [(validate.rules).enum = {in: [1, 2]}]; // 平台类型（Android、IOS）
  string app_id = 14 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 应用ID（Android：package_name；IOS：bundle_id）
  string app_download_link = 15 [(validate.rules).string = {ignore_empty: true, uri: true, max_len: 255}]; // APP下载地址

  repeated string prompts = 21 [(validate.rules).repeated = {ignore_empty: true, items: {string: {pattern: "(?:^prompt_config_id:.+?)"}}}]; // Prompt配置ID列表
}
message CreateApplicationConfigurationResp {
  ApplicationConfiguration configuration = 1;
}

message RemoveApplicationConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string config_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^application_config_id:.+?)"}}}]; // 应用配置ID列表
}
message RemoveApplicationConfigurationResp {}

message ModifyApplicationConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string config_id = 2 [(validate.rules).string = {pattern: "(?:^application_config_id:.+?)"}]; // 应用配置ID

  string name = 11 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 应用配置名称
  string description = 12 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 应用配置描述
  common.PlatformType platform_type = 13 [(validate.rules).enum = {in: [1, 2]}]; // 平台类型（Android、IOS）
  string app_id = 14 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 应用ID（Android：package_name；IOS：bundle_id）
  string app_download_link = 15 [(validate.rules).string = {ignore_empty: true, uri: true, max_len: 255}]; // APP下载地址

  repeated string prompts = 21 [(validate.rules).repeated = {ignore_empty: true, items: {string: {pattern: "(?:^prompt_config_id:.+?)"}}}]; // Prompt配置ID列表
}
message ModifyApplicationConfigurationResp {
  ApplicationConfiguration configuration = 1;
}

message SearchApplicationConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID

  sqlbuilder.Condition condition = 11; // 查询条件
  sqlbuilder.Pagination pagination = 12; // 查询分页
  repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchApplicationConfigurationResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;

  repeated ApplicationConfiguration items = 11;
}

message ViewApplicationConfigurationReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string config_id = 2 [(validate.rules).string = {pattern: "(?:^application_config_id:.+?)"}]; // 应用配置ID
}
message ViewApplicationConfigurationResp {
  ApplicationConfiguration configuration = 1;
}

message SearchApplicationConfigurationReferenceReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string config_id = 2 [(validate.rules).string = {pattern: "(?:^application_config_id:.+?)"}]; // 应用配置ID

  sqlbuilder.Condition condition = 11; // 查询条件
  sqlbuilder.Pagination pagination = 12; // 查询分页
  repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchApplicationConfigurationReferenceResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;

  repeated SearchApplicationConfigurationReferenceItem items = 11;
}


//UserDeviceService 用户设备服务
service UserDeviceService {
  // GetRemoteAndroidSerial 获取远程Android设备的设备编号
  rpc GetRemoteAndroidSerial(GetRemoteAndroidSerialReq) returns (GetRemoteAndroidSerialResp);
}

message GetRemoteAndroidSerialReq {
  common.DeviceType device_type = 1 [(validate.rules).enum = {not_in: [0]}]; // 设备类型
  string remote_address = 2 [(validate.rules).string = {min_len: 1}]; // 远程连接地址
}
message GetRemoteAndroidSerialResp {
  string serial = 1;
}


//UIAgentComponentService `UI Agent`组件服务
service UIAgentComponentService {
  //CreateUIAgentComponent 创建`UI Agent`组件
  rpc CreateUIAgentComponent(CreateUIAgentComponentReq) returns (CreateUIAgentComponentResp);
  //RemoveUIAgentComponent 删除`UI Agent`组件
  rpc RemoveUIAgentComponent(RemoveUIAgentComponentReq) returns (RemoveUIAgentComponentResp);
  //ModifyUIAgentComponent 编辑`UI Agent`组件
  rpc ModifyUIAgentComponent(ModifyUIAgentComponentReq) returns (ModifyUIAgentComponentResp);
  //SearchUIAgentComponent 搜索`UI Agent`组件
  rpc SearchUIAgentComponent(SearchUIAgentComponentReq) returns (SearchUIAgentComponentResp);
  //ViewUIAgentComponent 查看`UI Agent`组件
  rpc ViewUIAgentComponent(ViewUIAgentComponentReq) returns (ViewUIAgentComponentResp);
  //DiffUIAgentComponent 比较`UI Agent`组件
  rpc DiffUIAgentComponent(DiffUIAgentComponentReq) returns (DiffUIAgentComponentResp);

  //UpdateUIAgentComponentResult 更新`UI Agent`组件的执行结果
  rpc UpdateUIAgentComponentResult(UpdateUIAgentComponentResultReq) returns (UpdateUIAgentComponentResultResp);
}

message CreateUIAgentComponentReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID

  string name = 11 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 组件名称
  string description = 12 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 组件描述
  repeated string tags = 13 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len:1, max_len: 64}}}]; // 组件标签
  string application_id = 14 [(validate.rules).string = {pattern: "(?:^application_config_id:.+?)"}]; // 应用配置ID
  common.UIAgentMode mode = 15 [(validate.rules).enum = {not_in: [0]}]; // 模式（Agent模式、Step模式）
  repeated common.UIAgentComponentStep agent_mode_steps = 16 [(validate.rules).repeated = {ignore_empty: true}]; // Agent模式的步骤列表
  repeated common.UIAgentComponentStep step_mode_steps = 17 [(validate.rules).repeated = {ignore_empty: true}]; // Step模式的步骤列表
  common.UIAgentComponentExpectation expectation = 18; // 期望结果
  repeated common.GeneralConfigVar variables = 19 [deprecated = true, (validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 变量列表
  repeated common.UIAgentInputParameter input_parameters = 20 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 入参列表
  repeated common.UIAgentOutputParameter output_parameters = 21 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 出参列表
  bool foreground_check = 22; // 是否检查App在前台

  string maintained_by = 31 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 维护者
}
message CreateUIAgentComponentResp {
  UIAgentComponent component = 1;
}

message RemoveUIAgentComponentReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  repeated string component_ids = 2 [(validate.rules).repeated = {min_items: 1, unique: true, items: {string: {pattern: "(?:^ui_agent_component_id:.+?)"}}}]; // UIAgent组件ID列表
}
message RemoveUIAgentComponentResp {}

message ModifyUIAgentComponentReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID
  string component_id = 3 [(validate.rules).string = {pattern: "(?:^ui_agent_component_id:.+?)"}]; // 组件ID

  string name = 11 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 组件名称
  string description = 12 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 组件描述
  repeated string tags = 13 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len:1, max_len: 64}}}]; // 组件标签
  string application_id = 14 [(validate.rules).string = {pattern: "(?:^application_config_id:.+?)"}]; // 应用配置ID
  common.UIAgentMode mode = 15 [(validate.rules).enum = {not_in: [0]}]; // 模式（Agent模式、Step模式）
  repeated common.UIAgentComponentStep agent_mode_steps = 16 [(validate.rules).repeated = {ignore_empty: true}]; // Agent模式的步骤列表
  repeated common.UIAgentComponentStep step_mode_steps = 17 [(validate.rules).repeated = {ignore_empty: true}]; // Step模式的步骤列表
  common.UIAgentComponentExpectation expectation = 18; // 期望结果
  repeated common.GeneralConfigVar variables = 19 [deprecated = true, (validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 变量列表
  repeated common.UIAgentInputParameter input_parameters = 20 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 入参列表
  repeated common.UIAgentOutputParameter output_parameters = 21 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 出参列表
  bool foreground_check = 22; // 是否检查App在前台

  CommonState state = 31 [(validate.rules).enum = {in: [1, 2]}]; // 组件状态
  string maintained_by = 32 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 维护者
}
message ModifyUIAgentComponentResp {
  UIAgentComponent component = 1;
}

message SearchUIAgentComponentReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string category_id = 2 [(validate.rules).string = {pattern: "(?:^category_id:.+?)"}]; // 分类ID

  sqlbuilder.Condition condition = 11; // 查询条件
  sqlbuilder.Pagination pagination = 12; // 查询分页
  repeated sqlbuilder.SortField sort = 13 [(validate.rules).repeated.ignore_empty = true]; // 查询排序
}
message SearchUIAgentComponentResp {
  uint64 current_page = 1;
  uint64 page_size = 2;
  uint64 total_count = 3;
  uint64 total_page = 4;

  repeated UIAgentComponent items = 11;
}

message ViewUIAgentComponentReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string component_id = 2 [(validate.rules).string = {pattern: "(?:^ui_agent_component_id:.+?)"}]; // UIAgent组件ID
}
message ViewUIAgentComponentResp {
  UIAgentComponent component = 1;
}

message DiffUIAgentComponentReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string component_id = 2 [(validate.rules).string = {pattern: "(?:^ui_agent_component_id:.+?)"}]; // 组件ID

  repeated DiffField diff_fields = 11 [(validate.rules).repeated = {ignore_empty: true, items: {enum: {not_in: [0]}}}]; // 需要比较的字段列表
  bool fast_fail = 12; // 是否有一个字段有变更就退出判断

  string name = 21 [(validate.rules).string = {ignore_empty: true, min_len: 1, max_len: 64}]; // 组件名称
  string description = 22 [(validate.rules).string = {ignore_empty: true, max_len: 255}]; // 组件描述
  repeated string tags = 23 [(validate.rules).repeated = {ignore_empty: true, items: {string: {min_len:1, max_len: 64}}}]; // 组件标签
  string application_id = 24 [(validate.rules).string = {ignore_empty: true, pattern: "(?:^application_config_id:.+?)"}]; // 应用配置ID
  common.UIAgentMode mode = 25 [(validate.rules).enum = {defined_only: true}]; // 模式（Agent模式、Step模式）
  repeated common.UIAgentComponentStep agent_mode_steps = 26 [(validate.rules).repeated = {ignore_empty: true}]; // Agent模式的步骤列表
  repeated common.UIAgentComponentStep step_mode_steps = 27 [(validate.rules).repeated = {ignore_empty: true}]; // Step模式的步骤列表
  common.UIAgentComponentExpectation expectation = 28; // 期望结果
  repeated common.GeneralConfigVar variables = 29 [deprecated = true, (validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 变量列表
  repeated common.UIAgentInputParameter input_parameters = 30 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 入参列表
  repeated common.UIAgentOutputParameter output_parameters = 31 [(validate.rules).repeated = {ignore_empty: true, items: {message: {required: true}}}]; // 出参列表
  bool foreground_check = 32; // 是否检查App在前台
}
message DiffUIAgentComponentResp {
  bool changed = 1;
  repeated DiffDetail details = 2;
}

message UpdateUIAgentComponentResultReq {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string component_id = 2 [(validate.rules).string = {pattern: "(?:^ui_agent_component_id:.+?)"}]; // 组件ID

  int64 executed_at = 11; // 执行时间
  common.ExecutedResult result = 12; // 执行结果
}
message UpdateUIAgentComponentResultResp {}
