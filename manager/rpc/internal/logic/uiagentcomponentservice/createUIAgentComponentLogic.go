package uiagentcomponentservicelogic

import (
	"context"
	"database/sql"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	userpb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateUIAgentComponentLogic struct {
	*BaseLogic
}

func NewCreateUIAgentComponentLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateUIAgentComponentLogic {
	return &CreateUIAgentComponentLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateUIAgentComponent 创建UIAgent组件
func (l *CreateUIAgentComponentLogic) CreateUIAgentComponent(in *pb.CreateUIAgentComponentReq) (
	out *pb.CreateUIAgentComponentResp, err error,
) {
	var (
		projectID        = in.GetProjectId()
		categoryID       = in.GetCategoryId()
		applicationID    = in.GetApplicationId()
		mode             = in.GetMode()
		agentModeSteps   = in.GetAgentModeSteps()
		stepModeSteps    = in.GetStepModeSteps()
		variables        = in.GetVariables()
		inputParameters  = in.GetInputParameters()
		outputParameters = in.GetOutputParameters()
		expectation      = in.GetExpectation()
	)

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
		return nil, err
	}

	var c *model.Category
	// validate the category_id in req
	if c, err = model.CheckCategoryByCategoryId(
		l.ctx, l.svcCtx.CategoryModel, projectID, common.ConstCategoryTreeTypeUIAgentComponent, categoryID,
	); err != nil {
		return nil, err
	} else if c.CategoryType != common.ConstCategoryTypeDirectory {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the type of parent category[%s] does not support creation of sub category",
			c.CategoryType,
		)
	}

	var app *model.ApplicationConfiguration
	// validate the application_id in req
	if app, err = model.CheckApplicationConfigurationByConfigID(
		l.ctx, l.svcCtx.ApplicationConfigModel, projectID, applicationID,
	); err != nil {
		return nil, err
	}

	if mode == commonpb.UIAgentMode_UIAgentMode_AGENT && len(agentModeSteps) == 0 {
		return nil, errorx.Err(errorx.ProhibitedBehavior, "the agent mode steps cannot be empty when the mode is agent")
	} else if mode == commonpb.UIAgentMode_UIAgentMode_STEP && len(stepModeSteps) == 0 {
		return nil, errorx.Err(errorx.ProhibitedBehavior, "the step mode steps cannot be empty when the mode is step")
	}

	varNames := getVariableNames(variables) // compatible with old version data
	inParamNames := append(getInputParameterNames(inputParameters), varNames...)
	outParamNames := getOutputParameterNames(outputParameters)
	if mode == commonpb.UIAgentMode_UIAgentMode_STEP {
		// validate step_mode_steps in req
		if err = validateSteps(stepModeSteps, inParamNames, outParamNames); err != nil {
			return nil, err
		}
	} else {
		// validate agent_mode_steps in req
		if err = validateSteps(agentModeSteps, inParamNames, outParamNames); err != nil {
			return nil, err
		}
	}
	// validate expectation in req
	if err = validateExpectation(expectation, inParamNames, outParamNames); err != nil {
		return nil, err
	}

	// get images from step_mode_steps and expectation
	images, err := l.getImagesFromStepsAndExpectation(projectID, stepModeSteps, expectation)
	if err != nil {
		return nil, err
	}

	component, err := l.create(in, app, images)
	if err != nil {
		return nil, err
	}

	out = &pb.CreateUIAgentComponentResp{
		Component: &pb.UIAgentComponent{},
	}
	if err = utils.Copy(out.Component, component, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy ui agent component to response, component: %s, error: %+v",
			jsonx.MarshalIgnoreError(component), err,
		)
	}

	return out, nil
}

func (l *CreateUIAgentComponentLogic) create(
	in *pb.CreateUIAgentComponentReq, app *model.ApplicationConfiguration, images []*model.UiAgentImage,
) (
	*model.UiAgentComponent, error,
) {
	var (
		projectID       = in.GetProjectId()
		description     = in.GetDescription()
		variables       = in.GetVariables()
		inputParameters = in.GetInputParameters()
		maintainedBy    = in.GetMaintainedBy()

		tags, expectation sql.NullString
	)

	componentID, err := l.generateComponentID(projectID)
	if err != nil {
		return nil, err
	}

	if len(in.GetTags()) > 0 {
		// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
		tags.String = jsonx.MarshalToStringIgnoreError(in.GetTags())
		tags.Valid = true
	}

	if in.GetExpectation() != nil && (in.GetExpectation().GetText() != "" || len(in.GetExpectation().GetImages()) != 0) {
		expectation.String = protobuf.MarshalJSONToStringIgnoreError(in.GetExpectation())
		expectation.Valid = true
	}

	if len(variables) > 0 && len(inputParameters) == 0 {
		// compatible with old version data
		for _, v := range variables {
			inputParameters = append(
				inputParameters, &commonpb.UIAgentInputParameter{
					Key:   v.GetKey(),
					Value: v.GetValue(),
				},
			)
		}
	}

	if maintainedBy != "" {
		var user *userpb.UserInfo
		if user, err = l.getUserInfoByAccount(maintainedBy); err != nil {
			return nil, err
		} else if !user.GetEnabled() {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot assign a disabled user as a maintainer of stability plan, project_id: %s, maintained_by: %s",
				projectID, maintainedBy,
			)
		}
	} else {
		// if the maintainer is not set, the creator is set as the maintainer
		maintainedBy = l.currentUser.Account
	}

	now := time.Now()
	component := &model.UiAgentComponent{
		ProjectId:   projectID,
		CategoryId:  in.GetCategoryId(),
		ComponentId: componentID,
		Name:        in.GetName(),
		Description: sql.NullString{
			String: description,
			Valid:  description != "",
		},
		State:            int64(pb.CommonState_CS_ENABLE),
		Tags:             tags,
		PlatformType:     app.PlatformType,
		ApplicationId:    app.ConfigId,
		Mode:             int64(in.GetMode()),
		AgentModeSteps:   protobuf.MarshalJSONWithMessagesToStringIgnoreError(in.GetAgentModeSteps()),
		StepModeSteps:    protobuf.MarshalJSONWithMessagesToStringIgnoreError(in.GetStepModeSteps()),
		Expectation:      expectation,
		Variables:        protobuf.MarshalJSONWithMessagesToStringIgnoreError(variables),
		InputParameters:  protobuf.MarshalJSONWithMessagesToStringIgnoreError(inputParameters),
		OutputParameters: protobuf.MarshalJSONWithMessagesToStringIgnoreError(in.GetOutputParameters()),
		ForegroundCheck:  cast.ToInt64(in.GetForegroundCheck()),
		LatestResult:     int64(commonpb.ExecutedResult_TER_INIT),
		MaintainedBy: sql.NullString{
			String: maintainedBy,
			Valid:  maintainedBy != "",
		},
		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: now,
		UpdatedAt: now,
	}

	// create ui agent component in a transaction
	if err = l.svcCtx.UIAgentComponentModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			// create ui agent component
			if _, err = l.svcCtx.UIAgentComponentModel.Insert(context, session, component); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.UIAgentComponentModel.Table(), jsonx.MarshalIgnoreError(component), err,
				)
			}

			// create tags and tag references of ui agent component
			if err = l.createTagLogic.CreateTagAndReferenceForInternal(
				context, session, types.CreateOrUpdateTagReference{
					ProjectId:     component.ProjectId,
					ReferenceType: common.ConstReferenceTypeUIAgentComponent,
					ReferenceId:   component.ComponentId,
					Tags:          in.GetTags(),
				},
			); err != nil {
				return err
			}

			// update the application configuration reference of ui agent component
			if err = l.updateApplicationConfigRelationship(context, session, component, app.ConfigId); err != nil {
				return err
			}

			// update the image reference of ui agent component
			if err = l.updateImageRelationship(context, session, component, images); err != nil {
				return err
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return component, nil
}
