package dbOperations

import (
	"bufio"
	"context"
	"database/sql"
	"flag"
	"fmt"
	"net/url"
	"os"
	"path"
	"reflect"
	"strconv"
	"strings"
	"testing"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
)

const (
	schemeOfMySQL = "mysql"
	schemeOfRedis = "redis"

	defaultTimeout = 10 * time.Minute
)

var (
	dataSource  = flag.String("data_source", "", "data source")
	command     = flag.String("command", "", "command")
	commandFile = flag.String("command_file", "", "command file")
	timeout     = flag.String("timeout", "", "timeout")

	timeoutDuration = defaultTimeout
)

func TestMain(m *testing.M) {
	if !flag.Parsed() {
		flag.Parse()
	}

	m.Run()
}

func TestDBOperations(t *testing.T) {
	if *dataSource == "" {
		t.Fatal("not set the `data_source`")
	}
	if *command == "" && *commandFile == "" {
		t.Fatal("not set the `command` or `command_file`")
	} else if *command != "" && *commandFile != "" {
		t.Fatal("set the `command` and `command_file` at the same time")
	}
	if *timeout != "" {
		if d, err := time.ParseDuration(*timeout); err == nil {
			timeoutDuration = d
		}
	}

	u, err := url.Parse(*dataSource)
	if err != nil {
		t.Fatalf("parse data source error: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeoutDuration)
	defer cancel()

	if strings.EqualFold(u.Scheme, schemeOfRedis) {
		var handler *redisHandler
		handler, err = newRedisHandler(ctx, u)
		if err != nil {
			t.Fatalf("new redis handler error: %v", err)
		}
		defer func() {
			_ = handler.Close()
		}()
		if *commandFile != "" {
			err = handler.executeRedisFile(*commandFile)
		} else {
			err = handler.executeRedisCommand(*command)
		}
	} else {
		var handler *mysqlHandler
		handler, err = newMySQLHandler(ctx, u)
		if err != nil {
			t.Fatalf("new mysql handler error: %v", err)
		}
		defer func() {
			_ = handler.Close()
		}()

		if *commandFile != "" {
			err = handler.executeSQLFile(*commandFile)
		} else {
			err = handler.executeSQLCommand(nil, *command)
		}
	}
	if err != nil {
		t.Fatalf("handle command error: %v", err)
	}
}

type (
	mysqlHandler struct {
		logx.Logger
		ctx context.Context
		db  *sql.DB
	}
	mysqlSession interface {
		Query(query string, args ...any) (*sql.Rows, error)
		QueryContext(ctx context.Context, query string, args ...any) (*sql.Rows, error)
		Exec(query string, args ...any) (sql.Result, error)
		ExecContext(ctx context.Context, query string, args ...any) (sql.Result, error)
	}
)

func newMySQLHandler(ctx context.Context, u *url.URL) (*mysqlHandler, error) {
	if u == nil {
		return nil, fmt.Errorf("url is nil")
	}

	dsn := getMySQLDSNFromURL(u)
	db, err := sql.Open(schemeOfMySQL, dsn)
	if err != nil {
		return nil, err
	}

	db.SetMaxIdleConns(64)
	db.SetMaxOpenConns(64)
	db.SetConnMaxLifetime(time.Minute)

	if err = db.PingContext(ctx); err != nil {
		return nil, err
	}

	return &mysqlHandler{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		db:     db,
	}, nil
}

func (h *mysqlHandler) Close() error {
	if h.db != nil {
		return h.db.Close()
	}

	return nil
}

func (h *mysqlHandler) executeSQLFile(filePath string) (err error) {
	// 读取 SQL 文件内容
	data, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read SQL file: %w", err)
	}

	// 分割 SQL 语句 (处理分号、注释和空行)
	statements := splitSQLStatements(string(data))
	if len(statements) > 0 {
		var tx *sql.Tx
		tx, err = h.db.Begin()
		if err != nil {
			return fmt.Errorf("failed to begin transaction: %w", err)
		}

		defer func() {
			if p := recover(); p != nil {
				if e := tx.Rollback(); e != nil {
					err = fmt.Errorf("recover from %#v, rollback failed: %w", p, e)
				} else {
					err = fmt.Errorf("recover from %#v", p)
				}
			} else if err != nil {
				if e := tx.Rollback(); e != nil {
					err = fmt.Errorf("transaction failed: %s, rollback failed: %w", err, e)
				}
			} else {
				err = tx.Commit()
			}
		}()

		for _, stmt := range statements {
			if strings.TrimSpace(stmt) == "" {
				continue // 跳过空语句
			}

			if err = h.executeSQLCommand(tx, stmt); err != nil {
				return fmt.Errorf("failed to execute SQL command: %w", err)
			}
		}
	}

	return nil
}

func (h *mysqlHandler) executeSQLCommand(session mysqlSession, command string) error {
	if session == nil {
		session = h.db
	}

	if strings.EqualFold(command[0:6], "select") {
		rows, err := session.QueryContext(h.ctx, command)
		if err != nil {
			return fmt.Errorf("failed to query sql, sql: %s, error: %w", command, err)
		}
		defer func() {
			_ = rows.Close()
		}()

		columns, err := rows.Columns()
		if err != nil {
			return fmt.Errorf("failed to get columns, sql: %s, error: %w", command, err)
		}

		tt, err := rows.ColumnTypes()
		if err != nil {
			return fmt.Errorf("failed to get column types, sql: %s, error: %w", command, err)
		}

		colTypes := make([]string, len(tt))
		types := make([]reflect.Type, len(tt))
		for i, tp := range tt {
			st := tp.ScanType()
			if st == nil {
				h.Errorf("scantype is null for column %q", tp.Name())
				continue
			}
			colTypes[i] = st.Name()
			types[i] = st
		}
		values := make([]any, len(tt))
		for i := range values {
			values[i] = reflect.New(types[i]).Interface()
		}

		h.Infof("columns: %s", strings.Join(columns, ", "))
		h.Infof("column types: %s", strings.Join(colTypes, ", "))
		for rows.Next() {
			err = rows.Scan(values...)
			if err != nil {
				return fmt.Errorf("failed to scan, sql: %s, error: %w", command, err)
			}

			h.Infof("values: %s", jsonx.MarshalIgnoreError(values))
		}
	} else {
		result, err := session.ExecContext(h.ctx, command)
		if err != nil {
			return fmt.Errorf("failed to exec sql, sql: %s, error: %w", command, err)
		}

		lastInsertId, _ := result.LastInsertId()
		rowsAffected, _ := result.RowsAffected()
		h.Infof("result: %d, %d", lastInsertId, rowsAffected)
	}

	return nil
}

func handleCommandOfMySQL(t *testing.T, ctx context.Context, u *url.URL) error {
	if u == nil {
		return nil
	}

	dsn := getMySQLDSNFromURL(u)
	db, err := sql.Open(schemeOfMySQL, dsn)
	if err != nil {
		return err
	}
	defer func() {
		_ = db.Close()
	}()

	db.SetMaxIdleConns(64)
	db.SetMaxOpenConns(64)
	db.SetConnMaxLifetime(time.Minute)

	if err = db.Ping(); err != nil {
		return err
	}

	if strings.HasPrefix(*command, "select") || strings.HasSuffix(*command, "SELECT") {
		rows, err := db.QueryContext(ctx, *command)
		if err != nil {
			return fmt.Errorf("query sql error: %v", err)
		}
		defer func() {
			_ = rows.Close()
		}()

		columns, err := rows.Columns()
		if err != nil {
			return fmt.Errorf("get columns error: %v", err)
		}

		tt, err := rows.ColumnTypes()
		if err != nil {
			return fmt.Errorf("get column types error: %v", err)
		}

		colTypes := make([]string, len(tt))
		types := make([]reflect.Type, len(tt))
		for i, tp := range tt {
			st := tp.ScanType()
			if st == nil {
				t.Errorf("scantype is null for column %q", tp.Name())
				continue
			}
			colTypes[i] = st.Name()
			types[i] = st
		}
		values := make([]any, len(tt))
		for i := range values {
			values[i] = reflect.New(types[i]).Interface()
		}

		t.Logf("columns: %s", strings.Join(columns, ", "))
		t.Logf("column types: %s", strings.Join(colTypes, ", "))
		for rows.Next() {
			err = rows.Scan(values...)
			if err != nil {
				return fmt.Errorf("scan values error: %v", err)
			}

			t.Logf("values: %s", jsonx.MarshalIgnoreError(values))
		}
	} else {
		result, err := db.ExecContext(ctx, *command)
		if err != nil {
			return fmt.Errorf("exec sql error: %v", err)
		}

		lastInsertId, _ := result.LastInsertId()
		rowsAffected, _ := result.RowsAffected()
		t.Logf("result: %d, %d", lastInsertId, rowsAffected)
	}

	return nil
}

func getMySQLDSNFromURL(u *url.URL) string {
	username := u.User.Username()
	password, _ := u.User.Password()
	hostname := u.Hostname()
	port := u.Port()
	dbname := path.Base(u.Path)
	params := u.Query()

	return fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?%s", username, password, hostname, port, dbname, params.Encode())
}

type redisHandler struct {
	logx.Logger
	ctx context.Context
	c   redis.UniversalClient
}

func newRedisHandler(ctx context.Context, u *url.URL) (*redisHandler, error) {
	options := getRedisOptionsFromURL(u)
	c := redis.NewUniversalClient(options)
	_, err := c.Ping(ctx).Result()
	if err != nil {
		return nil, err
	}

	return &redisHandler{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		c:      c,
	}, nil
}

func (h *redisHandler) Close() error {
	if h.c != nil {
		return h.c.Close()
	}

	return nil
}

func (h *redisHandler) executeRedisFile(filePath string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("failed to open command file: %w", err)
	}
	defer func() {
		if file != nil {
			_ = file.Close()
		}
	}()

	scanner := bufio.NewScanner(file)
	lineNumber := 0

	for scanner.Scan() {
		lineNumber++
		line := strings.TrimSpace(scanner.Text())

		// 跳过注释行和空行
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		if err = h.executeRedisCommand(line); err != nil {
			return fmt.Errorf("command failed at line %d: %w\nCommand: %s", lineNumber, err, line)
		}
	}

	if err = scanner.Err(); err != nil {
		return fmt.Errorf("failed to read file: %w", err)
	}

	return nil
}

func (h *redisHandler) executeRedisCommand(command string) error {
	commands := strings.Fields(command)
	args := make([]any, len(commands))
	for i, cmd := range commands {
		args[i] = cmd
	}
	result, err := h.c.Do(h.ctx, args...).Result()
	if err != nil {
		return err
	}

	h.Infof("command: %s, result: %v", command, result)
	return nil
}

func handleCommandOfRedis(t *testing.T, ctx context.Context, u *url.URL) error {
	if u == nil {
		return nil
	}

	options := getRedisOptionsFromURL(u)
	c := redis.NewUniversalClient(options)
	defer func() {
		_ = c.Close()
	}()

	_, err := c.Ping(ctx).Result()
	if err != nil {
		return err
	}

	commands := strings.Split(*command, " ")
	args := make([]any, len(commands))
	for i, cmd := range commands {
		args[i] = cmd
	}
	result, err := c.Do(ctx, args...).Result()
	if err != nil {
		return err
	}

	t.Logf("result: %v", result)
	return nil
}

func getRedisOptionsFromURL(u *url.URL) *redis.UniversalOptions {
	username := u.User.Username()
	password, _ := u.User.Password()
	hostname := u.Hostname()
	port := u.Port()
	dbname := path.Base(u.Path)

	db, err := strconv.Atoi(dbname)
	if err != nil {
		db = 0
	}

	return &redis.UniversalOptions{
		Addrs:        []string{fmt.Sprintf("%s:%s", hostname, port)},
		Username:     username,
		Password:     password,
		DB:           db,
		MaxRetries:   3,
		MinIdleConns: 8,
	}
}

// splitSQLStatements 安全分割`SQL`语句 (处理字符串中的分号和注释)
func splitSQLStatements(sqlContent string) []string {
	var (
		statements  []string
		currentStmt strings.Builder

		inString  = false
		inComment = false
		quoteChar = byte(0) // 用于跟踪字符串引号类型 (单引号或双引号)
	)

	for i := 0; i < len(sqlContent); i++ {
		char := sqlContent[i]

		// 处理注释 (单行 -- 和多行 /* ... */)
		if !inComment && char == '-' && i+1 < len(sqlContent) && sqlContent[i+1] == '-' {
			inComment = true
			i++ // 跳过第二个 '-'
			continue
		}
		if inComment && char == '\n' {
			inComment = false
			continue
		}
		if inComment {
			continue
		}

		// 处理字符串 (跳过字符串内的分号)
		if !inString && (char == '\'' || char == '"') {
			inString = true
			quoteChar = char
		} else if inString && char == quoteChar {
			inString = false
		}

		// 语句分隔符 (仅在非字符串/注释区域有效)
		if !inString && !inComment && char == ';' {
			stmt := strings.TrimSpace(currentStmt.String())
			if stmt != "" {
				statements = append(statements, stmt)
				currentStmt.Reset()
			}
			continue
		}

		currentStmt.WriteRune(rune(char))
	}

	// 添加最后一段语句
	if currentStmt.Len() > 0 {
		statements = append(statements, strings.TrimSpace(currentStmt.String()))
	}

	return statements
}

// getRedisCommandsFromFile 读取`Redis`命令文件，跳过注释和空行
func getRedisCommandsFromFile(filePath string) ([]string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer func() {
		if file != nil {
			_ = file.Close()
		}
	}()

	var commandLines []string
	scanner := bufio.NewScanner(file)
	lineNumber := 0

	for scanner.Scan() {
		lineNumber++
		line := strings.TrimSpace(scanner.Text())

		// 跳过注释行和空行
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		commandLines = append(commandLines, line)
	}

	if err = scanner.Err(); err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	return commandLines, nil
}
