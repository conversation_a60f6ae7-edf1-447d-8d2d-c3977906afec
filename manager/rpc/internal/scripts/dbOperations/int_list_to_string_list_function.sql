INSERT INTO `function` (`project_id`, `name`, `type`, `category`, `description`, `language`, `content`, `parameters`, `returns`, `example`, `version`, `latest`, `deleted`, `created_by`, `updated_by`)
VALUES ('project_id:NHZf5JJz1Bk_pcohAMr8p', 'int_list_to_string_list', 'CUSTOM', 'Other', '数字列表转为字符串列表', 'PYTHON', 'def int_list_to_string_list(int_list):
    string_list = [str(x) for x in int_list]
    return string_list', '[{"name": "int_list", "description": "数字列表", "type": "ARRAY", "variadic": false}]', '[{"name": "string_list", "description": "字符串列表", "type": "ARRAY", "variadic": false}]', 'int_list_to_string_list([1, 2, 3]) => ["1", "2", "3"]', 'version:20250928144105616:4pt-I', 1, 0, 'T4535', 'T4535');
