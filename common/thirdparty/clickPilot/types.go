package clickPilot

type IClient interface {
	OptimizeTaskStepFormat(steps string) (string, error)
	ScoreTaskStep(steps string) ([]*ScoreResult, error)
	OptimizeTaskStepContent(steps string) ([]*OptimizeResult, error)
	OptimizeTaskExpectationContent(expectation string) ([]*OptimizeResult, error)
	CreateUITask(task *UITask) error
	GetTaskStatus(taskID string) (TaskStatusData, error)
	GetTaskRecord(taskID string) ([]*StepRecord, error)
	GetTaskLog(taskID string) (string, error)
	StopTask(taskID string) error
	DeleteTask(taskID string) error
	CreateRefCase(refCase *RefCase) error
	DeleteRefCase(taskID string) error
	DeleteRefCaseStep(stepIDs ...int64) error
}

// Config ClickPilot客户端配置
type Config struct {
	BaseURL string `json:",default=http://localhost:8080"` // ClickPilot服务地址
}

// OptimizeResult 优化结果
type OptimizeResult struct {
	ID                int64  `json:"id"`
	OriginalSentence  string `json:"original_sentence"`  // 原始句子
	OptimizedSentence string `json:"optimized_sentence"` // 优化后的句子
	IsOptimized       string `json:"is_optimized"`       // 是否优化
}

// ScoreResult 评分结果
type ScoreResult struct {
	ID              int64    `json:"id"`
	StepSentence    string   `json:"step_sentence"`    // 步骤句子
	MissingElements []string `json:"missing_elements"` // 缺失的要素
	OptimizeAdvice  string   `json:"optimize_advice"`  // 优化建议
	Score           float64  `json:"score"`            // 分数
}

// TaskExpectResultImage 任务期望结果图片
type TaskExpectResultImage struct {
	ImageName string `json:"image_name"` // 图片名称
	ImagePath string `json:"image_path"` // 图片文件路径
}

// TaskExpectResult 任务期望结果
type TaskExpectResult struct {
	Text string `json:"text,omitempty"` // 期望结果文字描述
	// Deprecated: use `TaskExpectResult.Images` instead
	Image  string                   `json:"image,omitempty"`  // 期望结果图片路径（可选）
	Images []*TaskExpectResultImage `json:"images,omitempty"` // 期望结果图片列表
}

// TaskStep 任务步骤
type TaskStep struct {
	Step         string            `json:"step"`          // 步骤名称
	ExpectResult *TaskExpectResult `json:"expect_result"` // 每一步期望结果（可选）
	WaitTime     float32           `json:"wait_time"`     // 步骤完成后的等待时间
}

// Device 设备配置
type Device struct {
	Type    DeviceType     `json:"type"`              // android / ios
	Android *AndroidDevice `json:"android,omitempty"` // Android设备配置
	IOS     *IOSDevice     `json:"ios,omitempty"`     // iOS设备配置
}

// AndroidDevice Android设备配置
type AndroidDevice struct {
	URL string `json:"url"` // adb连接地址
}

// IOSDevice iOS设备配置
type IOSDevice struct {
	URL string `json:"url"` // iOS设备连接地址
}

type BaseResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// 优化任务步骤格式
type (
	// optimizeTaskStepFormatReq 优化任务步骤格式请求
	optimizeTaskStepFormatReq struct {
		Steps string `json:"steps"`
	}

	// optimizeTaskStepFormatRespData 优化任务步骤格式响应数据
	optimizeTaskStepFormatRespData struct {
		Steps string `json:"optimized_steps"`
	}

	// optimizeTaskStepFormatResp 优化任务步骤格式响应
	optimizeTaskStepFormatResp struct {
		BaseResp

		Data optimizeTaskStepFormatRespData `json:"data"`
	}
)

// 任务步骤评分
type (
	// scoreTaskStepReq 任务步骤评分请求
	scoreTaskStepReq struct {
		Steps string `json:"steps"`
	}

	scoreTaskStepRespData struct {
		Results []*ScoreResult `json:"score_results"`
	}

	// scoreTaskStepResp 任务步骤评分响应
	scoreTaskStepResp struct {
		BaseResp

		Data scoreTaskStepRespData `json:"data"`
	}
)

// 优化任务步骤内容
type (
	// optimizeTaskStepContentReq 优化任务步骤内容请求
	optimizeTaskStepContentReq struct {
		Steps string `json:"steps"`
	}

	// optimizeTaskStepContentRespData 优化任务步骤内容响应数据
	optimizeTaskStepContentRespData struct {
		Results []*OptimizeResult `json:"optimize_results"`
	}

	// optimizeTaskStepContentResp 优化任务步骤内容响应
	optimizeTaskStepContentResp struct {
		BaseResp

		Data optimizeTaskStepContentRespData `json:"data"`
	}
)

// 优化任务预期结果内容
type (
	// optimizeTaskExpectationContentReq 优化任务预期结果内容请求
	optimizeTaskExpectationContentReq struct {
		ExpectedResult string `json:"expected_result"`
	}

	// optimizeTaskExpectationContentRespData 优化任务预期结果内容响应数据
	optimizeTaskExpectationContentRespData struct {
		Results []*OptimizeResult `json:"optimize_results"`
	}

	// optimizeTaskExpectationContentResp 优化任务预期结果内容响应
	optimizeTaskExpectationContentResp struct {
		BaseResp

		Data optimizeTaskExpectationContentRespData `json:"data"`
	}
)

// 创建任务
type (
	// UITask UI任务
	UITask struct {
		TaskID                  string            `json:"task_id,omitempty"`                   // 任务ID
		TaskName                string            `json:"task_name"`                           // 测试用例名称
		TaskExpectResult        *TaskExpectResult `json:"task_expect_result"`                  // 整个用例期望结果
		ExecutionMode           ExecutionMode     `json:"execution_mode"`                      // 执行模式（Anget模式、Step模式）
		TaskStepByStep          []*TaskStep       `json:"task_step_by_step"`                   // 分步骤验证模式（可选）
		TaskAggregationStep     string            `json:"task_aggregation_step"`               // Deprecated: 分步骤聚合模式（可选）
		AgentType               AgentType         `json:"agent_type"`                          // android / ios，默认android
		AgentConfigID           string            `json:"agent_config_id"`                     // agent的开放Prompt，默认值'tt'
		Device                  *Device           `json:"device"`                              // 设备配置
		AppID                   string            `json:"app_id"`                              // 执行的软件app_id
		AppName                 string            `json:"app_name,omitempty"`                  // 应用名称
		AppDescription          string            `json:"app_description,omitempty"`           // 软件功能的介绍
		UIComponentInstructions string            `json:"ui_component_instructions,omitempty"` // UI组件操作说明
		SpecialScenarios        string            `json:"special_scenarios,omitempty"`         // 特殊场景处理说明
		IsRestart               bool              `json:"is_restart"`                          // 是否重启app
		ReferenceTaskID         string            `json:"reference_task_id,omitempty"`         // 参考任务ID
		AppForegroundCheck      bool              `json:"app_foreground_check"`                // 是否检查app在前台
	}

	// createUITaskRespData 创建UI任务响应数据
	createUITaskRespData struct {
		TaskID string `json:"task_id"`
	}

	// createUITaskResp 创建UI任务响应
	createUITaskResp struct {
		BaseResp

		Data createUITaskRespData `json:"data"`
	}
)

// 查询任务状态
type (
	TaskStatusData struct {
		Status  TaskStatus `json:"status"`  // 任务状态
		Message string     `json:"message"` // 状态描述
	}

	// getTaskStatusRespData 查询任务状态响应数据
	getTaskStatusRespData struct {
		Status  TaskStatus `json:"status"`  // 任务状态
		Message string     `json:"message"` // 状态描述
	}

	// getTaskStatusResp 查询任务状态响应
	getTaskStatusResp struct {
		BaseResp

		Data getTaskStatusRespData `json:"data"`
	}
)

// 查询任务执行步骤记录
type (
	StepRecord struct {
		StepID    int64      `json:"step_id"`    // 步骤ID
		Index     int64      `json:"index"`      // 步骤索引
		Name      string     `json:"name"`       // 步骤名称
		Thought   string     `json:"thought"`    // 决策思考过程
		Action    string     `json:"action"`     // 动作
		Status    StepStatus `json:"status"`     // 步骤状态
		Image     string     `json:"image"`      // 截图路径
		StartedAt string     `json:"started_at"` // 开始时间
		EndedAt   string     `json:"ended_at"`   // 结束时间
		CostTime  float64    `json:"cost_time"`  // 执行耗时
	}

	// getTaskRecordRespData 查询任务执行步骤记录响应数据
	getTaskRecordRespData struct {
		ID        int64      `json:"id"`         // 步骤ID
		Number    int64      `json:"number"`     // 步骤序号
		StepName  string     `json:"step_name"`  // 步骤名称
		Thought   string     `json:"thought"`    // 决策思考过程
		Cost      float64    `json:"cost"`       // 执行耗时（秒）
		ImagePath string     `json:"image_path"` // 截图路径
		Action    string     `json:"action"`     // 执行的动作
		Status    StepStatus `json:"status"`     // 步骤状态
		StartTime string     `json:"start_time"` // 开始时间
		EndTime   string     `json:"end_time"`   // 结束时间
	}

	// getTaskRecordResp 查询任务执行步骤记录响应
	getTaskRecordResp struct {
		BaseResp

		Data []*getTaskRecordRespData `json:"data"`
	}
)

// 查询任务日志
type (
	// getTaskLogResp 查询任务日志响应
	getTaskLogResp struct {
		BaseResp

		Data string `json:"data"` // 日志内容
	}
)

// 停止任务
type (
	// stopTaskReq 停止任务请求
	stopTaskReq struct {
		TaskID string `json:"task_id"`
	}

	// stopTaskRespData 停止任务响应数据
	stopTaskRespData struct {
		Success bool   `json:"success"`
		TaskID  string `json:"task_id"`
		Message string `json:"message"`
	}

	// stopTaskResp 停止任务响应
	stopTaskResp struct {
		BaseResp

		Data stopTaskRespData `json:"data"`
	}
)

// 删除任务
type (
	// deleteTaskReq 删除任务请求
	deleteTaskReq struct {
		TaskID string `json:"task_id"`
	}

	// deleteTaskRespData 删除任务响应数据
	deleteTaskRespData struct {
		Success bool   `json:"success"`
		TaskID  string `json:"task_id"`
		Message string `json:"message"`
	}

	// deleteTaskResp 删除任务响应
	deleteTaskResp struct {
		BaseResp

		Data deleteTaskRespData `json:"data"`
	}
)

// 创建参考用例
type (
	// RefCase 参考用例
	RefCase struct {
		NewTaskID    string  `json:"new_task_id"`    // 新任务ID
		SourceTaskID string  `json:"source_task_id"` // 原任务ID
		ActionIDs    []int64 `json:"action_ids"`     // 参考步骤ID列表
	}

	// createRefCaseRespData 创建参考用例响应数据
	createRefCaseRespData struct {
		TaskID        string `json:"task_id"`
		CopiedActions int64  `json:"copied_actions"`
		TotalActions  int64  `json:"total_actions"`
	}

	// createRefCaseResp 创建参考用例响应
	createRefCaseResp struct {
		BaseResp

		Data createRefCaseRespData `json:"data"`
	}
)

// 删除参考用例
type (
	// deleteRefCaseReq 删除参考用例请求
	deleteRefCaseReq struct {
		TaskID string `json:"task_id"`
	}

	// deleteRefCaseRespData 删除参考用例响应数据
	deleteRefCaseRespData struct {
		TaskID string `json:"task_id"`
	}

	// deleteRefCaseResp 删除参考用例响应
	deleteRefCaseResp struct {
		BaseResp

		Data deleteRefCaseRespData `json:"data"`
	}
)

// 删除参考用例步骤
type (
	// deleteRefCaseStepReq 删除参考用例步骤请求
	deleteRefCaseStepReq struct {
		ActionIDs []int64 `json:"action_ids"`
	}

	// deleteRefCaseStepReq 删除参考用例步骤请求
	deleteRefCaseStepRespData struct {
		ActionIDs     []int64 `json:"action_ids"`
		DeletedCount  int64   `json:"deleted_count"`
		TotalCount    int64   `json:"total_count"`
		FailedActions []int64 `json:"failed_actions"`
	}

	// deleteRefCaseStepReq 删除参考用例步骤请求
	deleteRefCaseStepResp struct {
		BaseResp

		Data deleteRefCaseStepRespData `json:"data"`
	}
)
