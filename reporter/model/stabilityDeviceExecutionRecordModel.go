package model

import (
	"context"

	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

var (
	_ StabilityDeviceExecutionRecordModel = (*customStabilityDeviceExecutionRecordModel)(nil)

	stabilityDeviceExecutionRecordInsertFields = stringx.Remove(stabilityDeviceExecutionRecordFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// StabilityDeviceExecutionRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customStabilityDeviceExecutionRecordModel.
	StabilityDeviceExecutionRecordModel interface {
		stabilityDeviceExecutionRecordModel
		types.DBModel

		withSession(session sqlx.Session) StabilityDeviceExecutionRecordModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *StabilityDeviceExecutionRecord) squirrel.InsertBuilder
		UpdateBuilder(data *StabilityDeviceExecutionRecord) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*StabilityDeviceExecutionRecord, error)

		GenerateSearchStabilityDeviceExecutionRecordSqlBuilder(req SearchStabilityDeviceExecutionRecordReq) (
			searchStabilityDeviceExecutionRecordSelectBuilder, searchStabilityDeviceExecutionRecordCountBuilder,
		)
		FindCountStabilityDeviceExecutionRecords(
			ctx context.Context, countBuilder searchStabilityDeviceExecutionRecordCountBuilder,
		) (int64, error)
		FindStabilityDeviceExecutionRecords(
			ctx context.Context, selectBuilder searchStabilityDeviceExecutionRecordSelectBuilder,
		) ([]*StabilityDeviceExecutionRecord, error)

		DeleteRecordByTaskID(ctx context.Context, session sqlx.Session, taskID string) error
	}

	customStabilityDeviceExecutionRecordModel struct {
		*defaultStabilityDeviceExecutionRecordModel

		conn sqlx.SqlConn
	}
)

// NewStabilityDeviceExecutionRecordModel returns a model for the database table.
func NewStabilityDeviceExecutionRecordModel(conn sqlx.SqlConn) StabilityDeviceExecutionRecordModel {
	return &customStabilityDeviceExecutionRecordModel{
		defaultStabilityDeviceExecutionRecordModel: newStabilityDeviceExecutionRecordModel(conn),
		conn: conn,
	}
}

func (m *customStabilityDeviceExecutionRecordModel) withSession(session sqlx.Session) StabilityDeviceExecutionRecordModel {
	return NewStabilityDeviceExecutionRecordModel(sqlx.NewSqlConnFromSession(session))
}

func (m *customStabilityDeviceExecutionRecordModel) Table() string {
	return m.table
}

func (m *customStabilityDeviceExecutionRecordModel) Fields() []string {
	return stabilityDeviceExecutionRecordFieldNames
}

func (m *customStabilityDeviceExecutionRecordModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *customStabilityDeviceExecutionRecordModel) InsertBuilder(data *StabilityDeviceExecutionRecord) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(stabilityDeviceExecutionRecordInsertFields...).Values()
}

func (m *customStabilityDeviceExecutionRecordModel) UpdateBuilder(data *StabilityDeviceExecutionRecord) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customStabilityDeviceExecutionRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(stabilityDeviceExecutionRecordFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customStabilityDeviceExecutionRecordModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customStabilityDeviceExecutionRecordModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customStabilityDeviceExecutionRecordModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*StabilityDeviceExecutionRecord, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*StabilityDeviceExecutionRecord
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

type searchStabilityDeviceExecutionRecordSelectBuilder struct {
	squirrel.SelectBuilder
}

type searchStabilityDeviceExecutionRecordCountBuilder struct {
	squirrel.SelectBuilder
}

func (m *customStabilityDeviceExecutionRecordModel) GenerateSearchStabilityDeviceExecutionRecordSqlBuilder(req SearchStabilityDeviceExecutionRecordReq) (
	searchStabilityDeviceExecutionRecordSelectBuilder, searchStabilityDeviceExecutionRecordCountBuilder,
) {
	var sb, scb squirrel.SelectBuilder

	sb = sqlbuilder.SearchOptions(
		m.SelectBuilder().Where("`task_id` = ? AND `plan_execute_id` = ? AND `project_id` = ?", req.TaskID, req.ExecuteID, req.ProjectID),
		sqlbuilder.WithCondition(m, req.Condition),
		sqlbuilder.WithPagination(m, req.Pagination),
		sqlbuilder.WithSort(m, req.Sort),
	)
	scb = sqlbuilder.SearchOptions(
		m.SelectCountBuilder().Where("`project_id` = ?", req.ProjectID),
		sqlbuilder.WithCondition(m, req.Condition),
	)

	return searchStabilityDeviceExecutionRecordSelectBuilder{SelectBuilder: sb}, searchStabilityDeviceExecutionRecordCountBuilder{SelectBuilder: scb}
}

func (m *customStabilityDeviceExecutionRecordModel) FindCountStabilityDeviceExecutionRecords(ctx context.Context, countBuilder searchStabilityDeviceExecutionRecordCountBuilder) (
	int64, error,
) {
	return m.FindCount(ctx, countBuilder.SelectBuilder)
}

func (m *customStabilityDeviceExecutionRecordModel) FindStabilityDeviceExecutionRecords(
	ctx context.Context, selectBuilder searchStabilityDeviceExecutionRecordSelectBuilder,
) ([]*StabilityDeviceExecutionRecord, error) {
	var resp []*StabilityDeviceExecutionRecord

	err := utils.FindRows(
		ctx, m.conn, selectBuilder.SelectBuilder, func() any {
			return &resp
		},
	)

	return resp, err
}

func (m *customStabilityDeviceExecutionRecordModel) DeleteRecordByTaskID(
	ctx context.Context, session sqlx.Session, taskID string,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`task_id` = ?", taskID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}

	return err
}
