package model

import (
	"context"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
)

var (
	_ UiAgentComponentExecutionRecordModel = (*customUiAgentComponentExecutionRecordModel)(nil)

	uiAgentComponentExecutionRecordInsertFields = stringx.Remove(
		uiAgentComponentExecutionRecordFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`",
		"`created_at`", "`updated_at`", "`deleted_at`",
	)
)

type (
	// UiAgentComponentExecutionRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customUiAgentComponentExecutionRecordModel.
	UiAgentComponentExecutionRecordModel interface {
		uiAgentComponentExecutionRecordModel
		types.DBModel

		withSession(session sqlx.Session) UiAgentComponentExecutionRecordModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *UiAgentComponentExecutionRecord) squirrel.InsertBuilder
		UpdateBuilder(data *UiAgentComponentExecutionRecord) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(
			ctx context.Context, selectBuilder squirrel.SelectBuilder,
		) ([]*UiAgentComponentExecutionRecord, error)

		FindCountByReq(ctx context.Context, req SearchUIAgentComponentExecutionRecordReq) (int64, error)
		FindAllByReq(
			ctx context.Context, req SearchUIAgentComponentExecutionRecordReq,
		) ([]*UiAgentComponentExecutionRecord, error)
		FindCountByImage(ctx context.Context, projectID, imageID string) (int64, error)
		FindAllByImage(ctx context.Context, projectID, imageID string) ([]*UiAgentComponentExecutionRecord, error)
		FindCountByUDID(ctx context.Context, req SearchUIAgentComponentExecutionRecordByUDIDReq) (int64, error)
		FindAllByUDID(
			ctx context.Context, req SearchUIAgentComponentExecutionRecordByUDIDReq,
		) ([]*UiAgentComponentExecutionRecord, error)
		FindCountByParent(ctx context.Context, req SearchUIAgentComponentExecutionRecordByParentReq) (int64, error)
		FindAllByParent(
			ctx context.Context, req SearchUIAgentComponentExecutionRecordByParentReq,
		) ([]*UiAgentComponentExecutionRecord, error)

		FindUIAgentComponentExecutionRecordByExecuteType(
			ctx context.Context, executeType commonpb.ExecuteType,
		) ([]*RedundantRecord, error)
		SetRecordHasCleaned(ctx context.Context, session sqlx.Session, taskID, projectID string) error
		DeleteRecordByTaskID(ctx context.Context, session sqlx.Session, taskID, projectID string) error
		DeleteRecordByComponentID(ctx context.Context, session sqlx.Session, projectID, componentID string) error
		DeleteBeforeNDaysRecords(ctx context.Context, session sqlx.Session, days int) error
	}

	customUiAgentComponentExecutionRecordModel struct {
		*defaultUiAgentComponentExecutionRecordModel

		conn sqlx.SqlConn
	}
)

// NewUiAgentComponentExecutionRecordModel returns a model for the database table.
func NewUiAgentComponentExecutionRecordModel(conn sqlx.SqlConn) UiAgentComponentExecutionRecordModel {
	return &customUiAgentComponentExecutionRecordModel{
		defaultUiAgentComponentExecutionRecordModel: newUiAgentComponentExecutionRecordModel(conn),
		conn: conn,
	}
}

func (m *customUiAgentComponentExecutionRecordModel) withSession(session sqlx.Session) UiAgentComponentExecutionRecordModel {
	return NewUiAgentComponentExecutionRecordModel(sqlx.NewSqlConnFromSession(session))
}

func (m *customUiAgentComponentExecutionRecordModel) Table() string {
	return m.table
}

func (m *customUiAgentComponentExecutionRecordModel) Fields() []string {
	return uiAgentComponentExecutionRecordFieldNames
}

func (m *customUiAgentComponentExecutionRecordModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *customUiAgentComponentExecutionRecordModel) InsertBuilder(data *UiAgentComponentExecutionRecord) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(uiAgentComponentExecutionRecordInsertFields...).Values(
		data.TaskId, data.ExecuteId, data.ParentExecuteId, data.ProjectId, data.ComponentId, data.ComponentName,
		data.TriggerMode, data.ExecuteType, data.ApplicationConfig, data.Mode, data.Steps, data.Expectation,
		data.Variables, data.InputParameters, data.OutputParameters, data.ForegroundCheck, data.Device, data.Reinstall,
		data.Restart, data.ReferenceId, data.Times, data.ExecutedSteps, data.Successes, data.Passes, data.Status,
		data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.ErrMsg, data.Cleaned, data.Deleted,
		data.CreatedBy, data.UpdatedBy, data.DeletedBy,
	)
}

func (m *customUiAgentComponentExecutionRecordModel) UpdateBuilder(data *UiAgentComponentExecutionRecord) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`task_id`":            data.TaskId,
		"`execute_id`":         data.ExecuteId,
		"`parent_execute_id`":  data.ParentExecuteId,
		"`project_id`":         data.ProjectId,
		"`component_id`":       data.ComponentId,
		"`component_name`":     data.ComponentName,
		"`trigger_mode`":       data.TriggerMode,
		"`execute_type`":       data.ExecuteType,
		"`application_config`": data.ApplicationConfig,
		"`mode`":               data.Mode,
		"`steps`":              data.Steps,
		"`expectation`":        data.Expectation,
		"`variables`":          data.Variables,
		"`input_parameters`":   data.InputParameters,
		"`output_parameters`":  data.OutputParameters,
		"`foreground_check`":   data.ForegroundCheck,
		"`device`":             data.Device,
		"`reinstall`":          data.Reinstall,
		"`restart`":            data.Restart,
		"`reference_id`":       data.ReferenceId,
		"`times`":              data.Times,
		"`executed_steps`":     data.ExecutedSteps,
		"`successes`":          data.Successes,
		"`passes`":             data.Passes,
		"`status`":             data.Status,
		"`executed_by`":        data.ExecutedBy,
		"`started_at`":         data.StartedAt,
		"`ended_at`":           data.EndedAt,
		"`cost_time`":          data.CostTime,
		"`err_msg`":            data.ErrMsg,
		"`cleaned`":            data.Cleaned,
		"`deleted`":            data.Deleted,
		"`created_by`":         data.CreatedBy,
		"`updated_by`":         data.UpdatedBy,
		"`deleted_by`":         data.DeletedBy,
		"`updated_at`":         data.UpdatedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customUiAgentComponentExecutionRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(uiAgentComponentExecutionRecordFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customUiAgentComponentExecutionRecordModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customUiAgentComponentExecutionRecordModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customUiAgentComponentExecutionRecordModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*UiAgentComponentExecutionRecord, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UiAgentComponentExecutionRecord
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

// FindCountByReq 根据请求条件统计`UI Agent`组件执行记录数量
func (m *customUiAgentComponentExecutionRecordModel) FindCountByReq(
	ctx context.Context, req SearchUIAgentComponentExecutionRecordReq,
) (int64, error) {
	countBuilder := m.SelectCountBuilder().Where(
		"`project_id` = ? AND `component_id` = ? AND `parent_execute_id` IS NULL",
		req.ProjectID, req.ComponentID,
	)

	// 应用搜索条件
	countBuilder = sqlbuilder.SearchOptions(
		countBuilder,
		sqlbuilder.WithCondition(m, req.Condition),
	)

	return m.FindCount(ctx, countBuilder)
}

// FindAllByReq 根据请求条件查找`UI Agent`组件执行记录
func (m *customUiAgentComponentExecutionRecordModel) FindAllByReq(
	ctx context.Context, req SearchUIAgentComponentExecutionRecordReq,
) ([]*UiAgentComponentExecutionRecord, error) {
	selectBuilder := m.SelectBuilder().Where(
		"`project_id` = ? AND `component_id` = ? AND `parent_execute_id` IS NULL",
		req.ProjectID, req.ComponentID,
	)

	// 应用搜索条件、分页和排序
	selectBuilder = sqlbuilder.SearchOptions(
		selectBuilder,
		sqlbuilder.WithCondition(m, req.Condition),
		sqlbuilder.WithPagination(m, req.Pagination),
		sqlbuilder.WithSort(m, req.Sort),
	)

	return m.FindNoCacheByQuery(ctx, selectBuilder)
}

func (m *customUiAgentComponentExecutionRecordModel) FindCountByImage(
	ctx context.Context, projectID, imageID string,
) (int64, error) {
	/*
		SQL:
		SELECT COUNT(*)
		FROM `ui_agent_component_execution_record`
		WHERE `project_id` = ?
		  AND (JSON_CONTAINS(`steps`->'$[*].expectation.image', ?) OR JSON_CONTAINS(`expectation`->'$.images[*].image', ?));
	*/

	jsonImageID := fmt.Sprintf(`"%s"`, imageID)
	countBuilder := m.SelectCountBuilder().
		Where(
			"`project_id` = ? AND (JSON_CONTAINS(`steps`->'$[*].expectation.image', ?) OR JSON_CONTAINS(`expectation`->'$.images[*].image', ?)) AND `cleaned` = ?",
			projectID, jsonImageID, jsonImageID, common.NotCleaned,
		)
	return m.FindCount(ctx, countBuilder)
}

func (m *customUiAgentComponentExecutionRecordModel) FindAllByImage(
	ctx context.Context, projectID, imageID string,
) ([]*UiAgentComponentExecutionRecord, error) {
	/*
		SQL:
		SELECT *
		FROM `ui_agent_component_execution_record`
		WHERE `project_id` = ?
		  AND (JSON_CONTAINS(`steps`->'$[*].expectation.image', ?) OR JSON_CONTAINS(`expectation`->'$.images[*].image', ?));
	*/

	jsonImageID := fmt.Sprintf(`"%s"`, imageID)
	selectBuilder := m.SelectBuilder().
		Where(
			"`project_id` = ? AND (JSON_CONTAINS(`steps`->'$[*].expectation.image', ?) OR JSON_CONTAINS(`expectation`->'$.images[*].image', ?)) AND `cleaned` = ?",
			projectID, jsonImageID, jsonImageID, common.NotCleaned,
		)
	return m.FindNoCacheByQuery(ctx, selectBuilder)
}

func (m *customUiAgentComponentExecutionRecordModel) FindCountByUDID(
	ctx context.Context, req SearchUIAgentComponentExecutionRecordByUDIDReq,
) (int64, error) {
	countBuilder := m.SelectCountBuilder()

	if req.UDID != "" {
		// 查询 device 字段中的 UDID，支持项目设备和用户设备两种格式
		// 项目设备：{"project_device": {"udid": "xxx", ...}}
		// 用户设备：{"user_device": {"udid": "xxx", ...}}
		countBuilder = countBuilder.Where(
			"(`device`->'$.project_device.udid' = ? OR `device`->'$.user_device.udid' = ?)",
			req.UDID, req.UDID,
		)
	}

	// 应用搜索条件
	countBuilder = sqlbuilder.SearchOptions(
		countBuilder,
		sqlbuilder.WithCondition(m, req.Condition),
	)

	return m.FindCount(ctx, countBuilder)
}

func (m *customUiAgentComponentExecutionRecordModel) FindAllByUDID(
	ctx context.Context, req SearchUIAgentComponentExecutionRecordByUDIDReq,
) ([]*UiAgentComponentExecutionRecord, error) {
	selectBuilder := m.SelectBuilder()

	if req.UDID != "" {
		// 查询 device 字段中的 UDID，支持项目设备和用户设备两种格式
		// 项目设备：{"project_device": {"udid": "xxx", ...}}
		// 用户设备：{"user_device": {"udid": "xxx", ...}}
		selectBuilder = selectBuilder.Where(
			"(`device`->'$.project_device.udid' = ? OR `device`->'$.user_device.udid' = ?)",
			req.UDID, req.UDID,
		)
	}

	// 应用搜索条件、分页和排序
	selectBuilder = sqlbuilder.SearchOptions(
		selectBuilder,
		sqlbuilder.WithCondition(m, req.Condition),
		sqlbuilder.WithPagination(m, req.Pagination),
		sqlbuilder.WithSort(m, req.Sort),
	)

	return m.FindNoCacheByQuery(ctx, selectBuilder)
}

func (m *customUiAgentComponentExecutionRecordModel) FindCountByParent(
	ctx context.Context, req SearchUIAgentComponentExecutionRecordByParentReq,
) (int64, error) {
	countBuilder := m.SelectCountBuilder().Where(
		"`task_id` = ? AND `parent_execute_id` = ? AND `project_id` = ?",
		req.TaskID, req.ParentExecuteID, req.ProjectID,
	)

	// 应用搜索条件
	countBuilder = sqlbuilder.SearchOptions(
		countBuilder,
		sqlbuilder.WithCondition(m, req.Condition),
	)

	return m.FindCount(ctx, countBuilder)
}

func (m *customUiAgentComponentExecutionRecordModel) FindAllByParent(
	ctx context.Context, req SearchUIAgentComponentExecutionRecordByParentReq,
) ([]*UiAgentComponentExecutionRecord, error) {
	selectBuilder := m.SelectBuilder().Where(
		"`task_id` = ? AND `parent_execute_id` = ? AND `project_id` = ?",
		req.TaskID, req.ParentExecuteID, req.ProjectID,
	)

	// 应用搜索条件、分页和排序
	selectBuilder = sqlbuilder.SearchOptions(
		selectBuilder,
		sqlbuilder.WithCondition(m, req.Condition),
		sqlbuilder.WithPagination(m, req.Pagination),
		sqlbuilder.WithSort(m, req.Sort),
	)

	return m.FindNoCacheByQuery(ctx, selectBuilder)
}

func (m *customUiAgentComponentExecutionRecordModel) FindUIAgentComponentExecutionRecordByExecuteType(
	ctx context.Context, executeType commonpb.ExecuteType,
) ([]*RedundantRecord, error) {
	/*
		SQL:
		SELECT `task_id`,
		       `execute_id`,
		       `project_id`,
		       `component_id` as `execute_type_id`,
		       `created_at`
		FROM `ui_agent_component_execution_record`
		WHERE `parent_execute_id` IS NULL
		  AND `execute_type` = ?
		  AND `cleaned` = ?
		ORDER BY `created_at` DESC;
	*/

	var resp []*RedundantRecord

	fields := []string{
		"`task_id`",
		"`execute_id`",
		"`project_id`",
		"`component_id` AS `execute_type_id`",
		"`created_at`",
	}
	query, values, err := squirrel.Select(fields...).
		From(m.table).
		Where(
			"`parent_execute_id` IS NULL AND `execute_type` = ? AND `cleaned` = ?",
			int64(executeType), common.NotCleaned,
		).
		OrderBy("`created_at` DESC").
		ToSql()
	if err != nil {
		return nil, err
	}

	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch {
	case err == nil:
		return resp, nil
	case errors.Is(err, sqlc.ErrNotFound):
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *customUiAgentComponentExecutionRecordModel) SetRecordHasCleaned(
	ctx context.Context, session sqlx.Session, taskID, projectID string,
) error {
	stmt, values, err := squirrel.Update(m.table).
		SetMap(
			squirrel.Eq{
				"`cleaned`":    common.HasCleaned,
				"`updated_at`": squirrel.Expr("`updated_at`"),
			},
		).
		Where("`task_id` = ? AND `project_id` = ?", taskID, projectID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}
	return err
}

func (m *customUiAgentComponentExecutionRecordModel) DeleteRecordByTaskID(
	ctx context.Context, session sqlx.Session, taskID, projectID string,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`task_id` = ? AND `project_id` = ?", taskID, projectID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}

	return err
}

func (m *customUiAgentComponentExecutionRecordModel) DeleteRecordByComponentID(
	ctx context.Context, session sqlx.Session, projectID, componentID string,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`project_id` = ? AND `component_id` = ?", projectID, componentID).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}
	return err
}

func (m *customUiAgentComponentExecutionRecordModel) DeleteBeforeNDaysRecords(
	ctx context.Context, session sqlx.Session, days int,
) error {
	stmt, values, err := squirrel.Delete(m.table).
		Where("`cleaned` = ? AND `created_at` < ?", common.HasCleaned, time.Now().AddDate(0, 0, -days)).
		ToSql()
	if err != nil {
		return err
	}

	if session != nil {
		_, err = session.ExecCtx(ctx, stmt, values...)
	} else {
		_, err = m.conn.ExecCtx(ctx, stmt, values...)
	}
	return err
}
