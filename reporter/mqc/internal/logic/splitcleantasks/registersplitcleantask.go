package splitcleantaskslogic

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/RichardKnop/machinery/v2/tasks"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/types"
)

type RegisterSplitCleanTaskLogic struct {
	*BaseLogic
}

func NewRegisterSplitCleanTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RegisterSplitCleanTaskLogic {
	return &RegisterSplitCleanTaskLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *RegisterSplitCleanTaskLogic) RegisterCheckTask(svcCtx *svc.ServiceContext) (id string, err error) {
	return svcCtx.ReporterConsumer.RegisterPeriodicTask(
		"* * * * *", "check_task", &tasks.Signature{
			UUID: utils.GenTaskId(),
			Name: constants.MQTaskTypeReporterCheckTask,
			Args: []tasks.Arg{},
		},
	)
}

func (l *RegisterSplitCleanTaskLogic) keepAliveReporter() {
	// 心跳，注意这里的过期时间必须比RegisterCheckTask执行周期要大1倍
	err := l.svcCtx.Redis.Setex(l.svcCtx.MQCName, "1", 185)
	if err != nil {
		logx.Errorf("register reporter %s fail: %s", l.svcCtx.MQCName, err)
	}
}

// CheckPeriodicCleanTaskStatus 检查有无资格注册"定时拆分清理任务"资格
func (l *RegisterSplitCleanTaskLogic) CheckPeriodicCleanTaskStatus() error {
	l.keepAliveReporter()

	// 争抢锁
	lockKey := "reporter_mqc_register_periodic_task_lock"
	// 定时器归属Key，Value值为当前拥有定时任务执行权的reporter
	belongKey := "reporter_mqc_register_periodic_task_belong_lock"

	// 检查注册定时器资格
	v, err := l.svcCtx.Redis.Get(belongKey)
	if err != nil {
		return err
	}
	if v != "" {
		// 自己是定时器拥有者
		if v == l.svcCtx.MQCName {
			l.Logger.Info(fmt.Sprintf("owner is self: %s", v))
			return nil
		}

		// 若有之前注册的定时任务，先删除
		for _, eId := range l.svcCtx.SplitCleanTaskList {
			l.svcCtx.ReporterConsumer.RemovePeriodicTask(eId)
		}

		// 检查定时器拥有者是否还在线
		ownerStatus, err := l.svcCtx.Redis.Get(v)
		if err != nil {
			return err
		}

		l.Logger.Info(fmt.Sprintf("owner: %s", v))
		if ownerStatus != "" {
			l.Logger.Info(fmt.Sprintf("owner %s is online! waitting", v))
			return nil
		}
	}
	if v == "" {
		l.Logger.Info("owner is None! try get lock")
	} else {
		l.Logger.Info(fmt.Sprintf("owner %s is offline! try get lock", v))
	}

	lock, err := redislock.NewRedisLockAndAcquire(
		l.svcCtx.Redis, lockKey, redislock.WithExpire(common.ConstLockExpireTime),
	)
	if err != nil {
		return err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	l.Logger.Info(fmt.Sprintf("%s delete belongKey", l.svcCtx.MQCName))
	// 删除belongKey
	_, err = l.svcCtx.Redis.Del(belongKey)
	if err != nil {
		return err
	}
	// 设置belongKey为自己的值
	_, err = l.svcCtx.Redis.Setnx(belongKey, l.svcCtx.MQCName)
	if err != nil {
		return err
	}
	l.Logger.Info(
		fmt.Sprintf(
			"%s success to set key %s, begin to register split clean task", l.svcCtx.MQCName, belongKey,
		),
	)

	eId, err := l.RegisterSplitCleanTask(l.svcCtx)
	if err != nil {
		return err
	}
	l.svcCtx.SplitCleanTaskList = append(l.svcCtx.SplitCleanTaskList, eId)

	return err
}

// RegisterSplitCleanTask 注册"定时拆分清理任务"任务，按执行类型区分配置
func (l *RegisterSplitCleanTaskLogic) RegisterSplitCleanTask(svcCtx *svc.ServiceContext) (EntryID string, err error) {
	var taskValue types.CleanConfigs

	executeTypeMap := map[string]*types.CleanConfig{}
	for _, v := range supportedExecuteTypes {
		executeTypeMap[v] = &types.CleanConfig{
			Type: v, Mode: svcCtx.Config.ReporterCleaner.DefaultMode, Value: svcCtx.Config.ReporterCleaner.DefaultValue,
		}
		for _, rule := range svcCtx.Config.ReporterCleaner.CleanRules {
			if v == rule.Type {
				executeTypeMap[v] = &types.CleanConfig{Type: rule.Type, Mode: rule.Mode, Value: rule.Value}
			}
		}
		taskValue.Items = append(taskValue.Items, executeTypeMap[v])
	}

	arg, err := json.Marshal(&taskValue)
	if err != nil {
		return strconv.Itoa(0), errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "定时拆分清理任务参数序列化失败, error: %s", err,
		)
	}

	return svcCtx.ReporterConsumer.RegisterPeriodicTask(
		svcCtx.Config.ReporterCleaner.CleanCron, "split_clean_task", &tasks.Signature{
			UUID: utils.GenTaskId(),
			Name: constants.MQTaskTypeReporterSplitCleanTask,
			Args: []tasks.Arg{
				{
					Value: arg,
					Type:  "[]byte",
				},
			},
		},
	)
}
