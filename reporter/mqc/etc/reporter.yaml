Name: mqc.reporter

Log:
  ServiceName: mqc.reporter
  Encoding: plain
  Level: info
  Path: /app/logs/reporter

Prometheus:
  Host: 0.0.0.0
  Port: 14203
  Path: /metrics

Telemetry:
  Name: rpc.reporter
  Endpoint: http://127.0.0.1:14268/api/traces
  Sampler: 1.0
  Batcher: jaeger

DevServer:
  Enabled: true
  Port: 20533

DB:
  DataSource: root:Quwan@2020@tcp(127.0.0.1:3306)/reporter?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

Redis:
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 5

Discovery:
  Target: 127.0.0.1:21511
  NonBlock: true
  Timeout: 0

ReporterConsumer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mq:mqc:reporter
  ConsumerTag: mqc_reporter
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 5
  MaxWorker: 1

ReporterProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mq:mqc:reporter
  Db: 5

ClickPilot:
  BaseURL: 'http://127.0.0.1:8000'

ReporterCleaner:
  CleanCron: "11 * * * ?" # every hour
  CleanRules:
    - Type: API_PLAN
      Mode: time
      Value: "48h" # 2days
      KeepDays: 2
  DefaultMode: number
  DefaultValue: "10"
  DefaultKeepDays: 7

FailedCaseCleaner:
  CronExpression: "0 1 * * ?" # every day 1:00
  KeepDays: 10

LocalPath: ./reports
