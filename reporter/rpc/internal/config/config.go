package config

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/clickPilot"
)

type Config struct {
	zrpc.RpcServerConf

	DB    types.DBConfig
	Cache cache.CacheConf

	ReporterConsumer consumer.Config
	ReporterProducer producer.Config

	ProbeDomain string
	Monitors    []MonitorConfig

	ClickPilot clickPilot.Config
}

func (c Config) ListenOn() string {
	return c.RpcServerConf.ListenOn
}

func (c Config) LogConfig() logx.LogConf {
	return c.ServiceConf.Log
}

type MonitorConfig struct {
	Env               string
	GrafanaBaseURL    string
	AppInsightBaseURL string
}
