package uireporterlogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type DeleteUIPlanRecordLogic struct {
	*BaseLogic
}

func NewDeleteUIPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteUIPlanRecordLogic {
	return &DeleteUIPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// DeleteUIPlanRecord 删除UI计划执行记录
func (l *DeleteUIPlanRecordLogic) DeleteUIPlanRecord(in *pb.DeleteUIPlanRecordReq) (
	out *pb.DeleteUIPlanRecordResp, err error,
) {
	var (
		projectID = in.GetProjectId()
		planID    = in.GetPlanId()
	)

	sb := l.svcCtx.UIPlanExecutionRecordModel.SelectBuilder().Where(
		"`project_id` = ? AND `plan_id` = ?", projectID, planID,
	)
	records, err := l.svcCtx.UIPlanExecutionRecordModel.FindByQuery(l.ctx, sb)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find ui plan execution records, project_id: %s, plan_id: %s, error: %+v",
			projectID, planID, err,
		)
	}

	_ = mr.MapReduceVoid[*model.UiPlanExecutionRecord, any](
		func(source chan<- *model.UiPlanExecutionRecord) {
			for _, record := range records {
				if record == nil {
					continue
				}

				source <- record
			}
		},
		func(item *model.UiPlanExecutionRecord, writer mr.Writer[any], cancel func(error)) {
			if item == nil {
				return
			}

			payload := jsonx.MarshalIgnoreError(
				&common.SetCleanedTask{
					TaskId:    item.TaskId,
					ExecuteId: item.ExecuteId,
					ProjectId: item.ProjectId,
					CleanType: constants.CleanTypeUiPlan,
				},
			)
			if _, err := l.svcCtx.ReporterProducer.Send(
				l.ctx, base.NewTask(
					constants.MQTaskTypeReporterSetCleanedTask,
					payload,
					base.WithQueueOptions(constants.MQNameReporterMQC),
				), base.QueuePriorityDefault,
			); err != nil {
				l.Errorf(
					"failed to send task to mq, type: %s, payload: %s, error: %+v",
					constants.MQTaskTypeReporterSetCleanedTask, payload, err,
				)
				return
			}

			l.Infof(
				"send delete ui plan records task to mq successfully, type: %s, payload: %s",
				constants.MQTaskTypeReporterSetCleanedTask, payload,
			)
		},
		func(pipe <-chan any, cancel func(error)) {
		},
	)

	if err = l.svcCtx.UIPlanExecutionRecordModel.DeleteRecordByPlanID(l.ctx, nil, projectID, planID); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to delete ui plan execution record, project_id: %s, plan_id: %s, error: %+v",
			projectID, planID, err,
		)
	}

	return &pb.DeleteUIPlanRecordResp{}, nil
}
