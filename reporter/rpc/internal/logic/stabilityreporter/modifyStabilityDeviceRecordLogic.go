package stabilityreporterlogic

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ModifyStabilityDeviceRecordLogic struct {
	*BaseLogic
}

func NewModifyStabilityDeviceRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyStabilityDeviceRecordLogic {
	return &ModifyStabilityDeviceRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyStabilityDeviceRecord 修改稳测设备的执行记录
func (l *ModifyStabilityDeviceRecordLogic) ModifyStabilityDeviceRecord(in *pb.PutStabilityDeviceRecordReq) (out *pb.ModifyStabilityDeviceRecordResp, err error) {
	var (
		taskID     = in.GetTaskId()
		executeID  = in.GetExecuteId()
		projectID  = in.GetProjectId()
		executedBy = in.GetExecutedBy()
	)

	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockStabilityDeviceRecordTaskIDExecuteIDProjectIDPrefix, taskID, executeID, projectID,
	)

	fn := func() error {
		origin, err := l.svcCtx.StabilityDeviceExecutionRecordModel.FindOneByTaskIdExecuteIdProjectId(
			l.ctx, taskID, executeID, projectID,
		)
		if err != nil {
			if !errors.Is(err, model.ErrNotFound) {
				return errorx.Errorf(
					errorx.DBError,
					"failed to find stability device execution record, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
					taskID, executeID, projectID, err,
				)
			} else {
				return errorx.Errorf(
					errorx.NotExists,
					"the stability device execution record doesn't exist, task_id: %s, execute_id: %s, project_id: %s",
					taskID, executeID, projectID,
				)
			}
		}

		activities := make(map[string]int64)
		if in.GetResult() != nil {
			activityStatistics := in.GetResult().GetActivityStatistics()
			if activityStatistics != nil {
				for _, activity := range activityStatistics.GetTotalActivity() {
					activities[activity] = 0
				}
				for _, activity := range activityStatistics.GetTestedActivity() {
					activities[activity] = 1
				}
			}
			in.Result.ActivityStatistics = nil
		}

		record := l.ConvertStabilityDeviceModel(in, origin)
		if _, err = l.svcCtx.StabilityDeviceExecutionRecordModel.Update(l.ctx, nil, record); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to modify stability device execution record, task_id: %s, execute_id: %s, project_id: %s, plan_execute_id: %s, error: %+v",
				taskID, executeID, projectID, record.PlanExecuteId, err,
			)
		}

		return mr.MapReduceVoid[string, any](
			func(source chan<- string) {
				for activity := range activities {
					source <- activity
				}
			}, func(item string, writer mr.Writer[any], cancel func(error)) {
				_, err := l.svcCtx.StabilityDeviceActivityRecordModel.Insert(l.ctx, nil, &model.StabilityDeviceActivityRecord{
					TaskId:    taskID,
					ExecuteId: executeID,
					ProjectId: projectID,
					Udid:      record.Udid,
					Name:      item,
					Covered:   activities[item],
					Deleted:   int64(constants.NotDeleted),
					CreatedBy: executedBy,
					UpdatedBy: executedBy,
					DeletedAt: sql.NullTime{},
				})
				if err != nil {
					l.Errorf(
						"failed to insert stability device activity record, task_id: %s, execute_id: %s, project_id: %s, udid: %s, name: %s, covered: %d, error: %+v",
						taskID, executeID, projectID, record.Udid, item, activities[item], err,
					)
				}
			}, func(pipe <-chan any, cancel func(error)) {
			}, mr.WithContext(l.ctx),
		)
	}
	if err = caller.LockWithOptionDo(l.svcCtx.Redis, key, fn, redislock.WithTimeout(lockTimeout)); err != nil {
		return nil, err
	}
	return &pb.ModifyStabilityDeviceRecordResp{}, nil
}
