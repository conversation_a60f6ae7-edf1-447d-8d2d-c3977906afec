package stabilityreporterlogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type DeleteStabilityPlanRecordLogic struct {
	*BaseLogic
}

func NewDeleteStabilityPlanRecordLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *DeleteStabilityPlanRecordLogic {
	return &DeleteStabilityPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// DeleteStabilityPlanRecord 删除稳定性测试执行记录
func (l *DeleteStabilityPlanRecordLogic) DeleteStabilityPlanRecord(in *pb.DeleteStabilityPlanRecordReq) (
	out *pb.DeleteStabilityPlanRecordResp, err error,
) {
	var (
		projectID = in.GetProjectId()
		planID    = in.GetPlanId()
	)

	sb, _ := l.svcCtx.StabilityPlanExecutionRecordModel.GenerateSearchStabilityPlanExecutionRecordSqlBuilder(
		model.SearchStabilityPlanExecutionRecordReq{
			BaseSearchReq: model.BaseSearchReq{
				ProjectID: projectID,
			},
			PlanID: planID,
		},
	)
	records, err := l.svcCtx.StabilityPlanExecutionRecordModel.FindStabilityPlanExecutionRecords(l.ctx, sb)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find stability plan execution records, project_id: %s, plan_id: %s, error: %+v",
			projectID, planID, err,
		)
	}

	_ = mr.MapReduceVoid[*model.StabilityPlanExecutionRecord, any](
		func(source chan<- *model.StabilityPlanExecutionRecord) {
			for _, record := range records {
				if record == nil {
					continue
				}

				source <- record
			}
		},
		func(item *model.StabilityPlanExecutionRecord, writer mr.Writer[any], cancel func(error)) {
			if item == nil {
				return
			}

			payload := jsonx.MarshalIgnoreError(
				&common.SetCleanedTask{
					TaskId:    item.TaskId,
					ExecuteId: item.ExecuteId,
					ProjectId: item.ProjectId,
					CleanType: constants.CleanTypeStabilityPlan,
				},
			)
			if _, err := l.svcCtx.ReporterProducer.Send(
				l.ctx, base.NewTask(
					constants.MQTaskTypeReporterSetCleanedTask,
					payload,
					base.WithQueueOptions(constants.MQNameReporterMQC),
				), base.QueuePriorityDefault,
			); err != nil {
				l.Errorf(
					"failed to send task to mq, type: %s, payload: %s, error: %+v",
					constants.MQTaskTypeReporterSetCleanedTask, payload, err,
				)
				return
			}

			l.Infof(
				"send delete stability plan records task to mq successfully, type: %s, payload: %s",
				constants.MQTaskTypeReporterSetCleanedTask, payload,
			)
		},
		func(pipe <-chan any, cancel func(error)) {
		},
	)

	if err = l.svcCtx.StabilityPlanExecutionRecordModel.DeleteRecordByPlanID(
		l.ctx, nil, projectID, planID,
	); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to delete stability plan execution record, project_id: %s, plan_id: %s, error: %+v",
			projectID, planID, err,
		)
	}

	return &pb.DeleteStabilityPlanRecordResp{}, nil
}
