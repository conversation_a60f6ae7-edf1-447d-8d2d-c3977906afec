package perfreporterlogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type DeletePerfPlanRecordLogic struct {
	*BaseLogic
}

func NewDeletePerfPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeletePerfPlanRecordLogic {
	return &DeletePerfPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// DeletePerfPlanRecord 删除压测计划执行记录
func (l *DeletePerfPlanRecordLogic) DeletePerfPlanRecord(in *pb.DeletePerfPlanRecordReq) (
	out *pb.DeletePerfPlanRecordResp, err error,
) {
	var (
		projectID = in.GetProjectId()
		planID    = in.GetPlanId()
	)

	records, err := l.svcCtx.PerfPlanExecutionRecordModel.FindAllByReq(
		l.ctx, model.SearchPerfPlanExecutionRecordReq{
			BaseSearchReq: model.BaseSearchReq{
				ProjectID: projectID,
			},
			PlanID: planID,
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf plan execution records, project_id: %s, plan_id: %s, error: %+v",
			projectID, planID, err,
		)
	}

	_ = mr.MapReduceVoid[*model.PerfPlanExecutionRecord, any](
		func(source chan<- *model.PerfPlanExecutionRecord) {
			for _, record := range records {
				if record == nil {
					continue
				}

				source <- record
			}
		},
		func(item *model.PerfPlanExecutionRecord, writer mr.Writer[any], cancel func(error)) {
			if item == nil {
				return
			}

			payload := jsonx.MarshalIgnoreError(
				&common.SetCleanedTask{
					TaskId:    item.TaskId,
					ExecuteId: item.ExecuteId,
					ProjectId: item.ProjectId,
					CleanType: constants.CleanTypePerfPlan,
				},
			)
			if _, err := l.svcCtx.ReporterProducer.Send(
				l.ctx, base.NewTask(
					constants.MQTaskTypeReporterSetCleanedTask,
					payload,
					base.WithQueueOptions(constants.MQNameReporterMQC),
				), base.QueuePriorityDefault,
			); err != nil {
				l.Errorf(
					"failed to send task to mq, type: %s, payload: %s, error: %+v",
					constants.MQTaskTypeReporterSetCleanedTask, payload, err,
				)
				return
			}

			l.Infof(
				"send delete perf plan records task to mq successfully, type: %s, payload: %s",
				constants.MQTaskTypeReporterSetCleanedTask, payload,
			)
		},
		func(pipe <-chan any, cancel func(error)) {
		},
	)

	if err = l.svcCtx.PerfPlanExecutionRecordModel.DeleteRecordByPlanID(l.ctx, nil, projectID, planID); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to delete perf plan execution record, project_id: %s, plan_id: %s, error: %+v",
			projectID, planID, err,
		)
	}

	return &pb.DeletePerfPlanRecordResp{}, nil
}
