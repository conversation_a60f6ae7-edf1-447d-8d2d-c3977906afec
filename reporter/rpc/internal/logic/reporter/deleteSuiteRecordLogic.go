package reporterlogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type DeleteSuiteRecordLogic struct {
	*BaseLogic
}

func NewDeleteSuiteRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteSuiteRecordLogic {
	return &DeleteSuiteRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// DeleteSuiteRecord 删除集合执行记录
func (l *DeleteSuiteRecordLogic) DeleteSuiteRecord(in *pb.DeleteSuiteRecordReq) (
	out *pb.DeleteSuiteRecordResp, err error,
) {
	var (
		projectID = in.GetProjectId()
		suiteID   = in.GetSuiteId()
	)

	sb := l.svcCtx.SuiteExecutionRecordModel.SelectBuilder().Where(
		"`project_id` = ? AND `suite_id` = ?", projectID, suiteID,
	)
	records, err := l.svcCtx.SuiteExecutionRecordModel.FindByQuery(l.ctx, sb)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find suite execution records, project_id: %s, suite_id: %s, error: %+v",
			projectID, suiteID, err,
		)
	}

	_ = mr.MapReduceVoid[*model.SuiteExecutionRecord, any](
		func(source chan<- *model.SuiteExecutionRecord) {
			for _, record := range records {
				if record == nil {
					continue
				}

				source <- record
			}
		},
		func(item *model.SuiteExecutionRecord, writer mr.Writer[any], cancel func(error)) {
			if item == nil {
				return
			}

			payload := jsonx.MarshalIgnoreError(
				&common.SetCleanedTask{
					TaskId:    item.TaskId,
					ExecuteId: item.ExecuteId,
					ProjectId: item.ProjectId,
					CleanType: constants.CleanTypeApiSuite,
				},
			)
			if _, err := l.svcCtx.ReporterProducer.Send(
				l.ctx, base.NewTask(
					constants.MQTaskTypeReporterSetCleanedTask,
					payload,
					base.WithQueueOptions(constants.MQNameReporterMQC),
				), base.QueuePriorityDefault,
			); err != nil {
				l.Errorf(
					"failed to send task to mq, type: %s, payload: %s, error: %+v",
					constants.MQTaskTypeReporterSetCleanedTask, payload, err,
				)
				return
			}

			l.Infof(
				"send delete api suite records task to mq successfully, type: %s, payload: %s",
				constants.MQTaskTypeReporterSetCleanedTask, payload,
			)
		},
		func(pipe <-chan any, cancel func(error)) {
		},
	)

	if err = l.svcCtx.SuiteExecutionRecordModel.DeleteRecordBySuiteID(l.ctx, nil, projectID, suiteID); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to delete suite execution record, project_id: %s, suite_id: %s, error: %+v",
			projectID, suiteID, err,
		)
	}

	return &pb.DeleteSuiteRecordResp{}, nil
}
