package reporterlogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type DeleteInterfaceRecordLogic struct {
	*BaseLogic
}

func NewDeleteInterfaceRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteInterfaceRecordLogic {
	return &DeleteInterfaceRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// DeleteInterfaceRecord 删除接口执行记录
func (l *DeleteInterfaceRecordLogic) DeleteInterfaceRecord(in *pb.DeleteInterfaceRecordReq) (
	out *pb.DeleteInterfaceRecordResp, err error,
) {
	var (
		projectID  = in.GetProjectId()
		documentID = in.GetInterfaceId()
	)

	sb := l.svcCtx.InterfaceExecutionRecordModel.SelectBuilder().Where(
		"`project_id` = ? AND `interface_id` = ?", projectID, documentID,
	)
	records, err := l.svcCtx.InterfaceExecutionRecordModel.FindByQuery(l.ctx, sb)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find interface execution records, project_id: %s, interface_id: %s, error: %+v",
			projectID, documentID, err,
		)
	}

	_ = mr.MapReduceVoid[*model.InterfaceExecutionRecord, any](
		func(source chan<- *model.InterfaceExecutionRecord) {
			for _, record := range records {
				if record == nil {
					continue
				}

				source <- record
			}
		},
		func(item *model.InterfaceExecutionRecord, writer mr.Writer[any], cancel func(error)) {
			if item == nil {
				return
			}

			payload := jsonx.MarshalIgnoreError(
				&common.SetCleanedTask{
					TaskId:    item.TaskId,
					ExecuteId: item.ExecuteId,
					ProjectId: item.ProjectId,
					CleanType: constants.CleanTypeInterfaceDocument,
				},
			)
			if _, err := l.svcCtx.ReporterProducer.Send(
				l.ctx, base.NewTask(
					constants.MQTaskTypeReporterSetCleanedTask,
					payload,
					base.WithQueueOptions(constants.MQNameReporterMQC),
				), base.QueuePriorityDefault,
			); err != nil {
				l.Errorf(
					"failed to send task to mq, type: %s, payload: %s, error: %+v",
					constants.MQTaskTypeReporterSetCleanedTask, payload, err,
				)
				return
			}

			l.Infof(
				"send delete interface records task to mq successfully, type: %s, payload: %s",
				constants.MQTaskTypeReporterSetCleanedTask, payload,
			)
		},
		func(pipe <-chan any, cancel func(error)) {
		},
	)

	if err = l.svcCtx.InterfaceExecutionRecordModel.DeleteRecordByDocumentID(
		l.ctx, nil, projectID, documentID,
	); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to delete interface execution record, project_id: %s, interface_id: %s, error: %+v",
			projectID, documentID, err,
		)
	}

	return &pb.DeleteInterfaceRecordResp{}, nil
}
