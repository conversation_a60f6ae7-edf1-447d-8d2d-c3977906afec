package reporterlogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type DeleteRecordLogic struct {
	*BaseLogic
}

func NewDeleteRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteRecordLogic {
	return &DeleteRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// DeleteRecord 删除组件执行记录
func (l *DeleteRecordLogic) DeleteRecord(in *pb.DeleteRecordReq) (out *pb.DeleteRecordResp, err error) {
	var (
		projectID     = in.GetProjectId()
		componentType = in.GetComponentType()
		componentID   = in.GetComponentId()
	)

	sb := l.svcCtx.ExecutionRecordModel.SelectBuilder().Where(
		"`project_id` = ? AND `component_id` = ? AND `execute_type` = ? AND `is_root` = 1", projectID, componentType,
		componentID,
	)
	records, err := l.svcCtx.ExecutionRecordModel.FindByQuery(l.ctx, sb)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find execution records, project_id: %s, component_id: %s, component_type: %s, error: %+v",
			projectID, componentID, componentType, err,
		)
	}

	_ = mr.MapReduceVoid[*model.ExecutionRecord, any](
		func(source chan<- *model.ExecutionRecord) {
			for _, record := range records {
				if record == nil {
					continue
				}

				source <- record
			}
		},
		func(item *model.ExecutionRecord, writer mr.Writer[any], cancel func(error)) {
			if item == nil {
				return
			}

			payload := jsonx.MarshalIgnoreError(
				&common.SetCleanedTask{
					TaskId:    item.TaskId,
					ExecuteId: item.ExecuteId,
					ProjectId: item.ProjectId,
					CleanType: componentType,
				},
			)
			if _, err := l.svcCtx.ReporterProducer.Send(
				l.ctx, base.NewTask(
					constants.MQTaskTypeReporterSetCleanedTask,
					payload,
					base.WithQueueOptions(constants.MQNameReporterMQC),
				), base.QueuePriorityDefault,
			); err != nil {
				l.Errorf(
					"failed to send task to mq, type: %s, payload: %s, error: %+v",
					constants.MQTaskTypeReporterSetCleanedTask, payload, err,
				)
				return
			}

			l.Infof(
				"send delete component records task to mq successfully, type: %s, payload: %s",
				constants.MQTaskTypeReporterSetCleanedTask, payload,
			)
		},
		func(pipe <-chan any, cancel func(error)) {
		},
	)

	if err = l.svcCtx.ExecutionRecordModel.DeleteRecordByComponentID(
		l.ctx, nil, projectID, componentID, componentType,
	); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to delete execution record, project_id: %s, component_id: %s, component_type: %s, error: %+v",
			projectID, componentID, componentType, err,
		)
	}

	return &pb.DeleteRecordResp{}, nil
}
