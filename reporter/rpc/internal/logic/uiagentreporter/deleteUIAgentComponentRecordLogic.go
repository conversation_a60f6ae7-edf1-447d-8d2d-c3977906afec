package uiagentreporterlogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type DeleteUIAgentComponentRecordLogic struct {
	*BaseLogic
}

func NewDeleteUIAgentComponentRecordLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *DeleteUIAgentComponentRecordLogic {
	return &DeleteUIAgentComponentRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// DeleteUIAgentComponentRecord 删除`UI Agent`组件执行记录
func (l *DeleteUIAgentComponentRecordLogic) DeleteUIAgentComponentRecord(in *pb.DeleteUIAgentComponentRecordReq) (
	out *pb.DeleteUIAgentComponentRecordResp, err error,
) {
	var (
		projectID   = in.GetProjectId()
		componentID = in.GetComponentId()
	)

	records, err := l.svcCtx.UiAgentComponentExecutionRecordModel.FindAllByReq(
		l.ctx, model.SearchUIAgentComponentExecutionRecordReq{
			BaseSearchReq: model.BaseSearchReq{
				ProjectID: projectID,
			},
			ComponentID: componentID,
		},
	)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find ui agent component execution records, project_id: %s, component_id: %s, error: %+v",
			projectID, componentID, err,
		)
	}

	_ = mr.MapReduceVoid[*model.UiAgentComponentExecutionRecord, any](
		func(source chan<- *model.UiAgentComponentExecutionRecord) {
			for _, record := range records {
				if record == nil {
					continue
				}

				source <- record
			}
		},
		func(item *model.UiAgentComponentExecutionRecord, writer mr.Writer[any], cancel func(error)) {
			if item == nil {
				return
			}

			payload := jsonx.MarshalIgnoreError(
				&common.SetCleanedTask{
					TaskId:    item.TaskId,
					ExecuteId: item.ExecuteId,
					ProjectId: item.ProjectId,
					CleanType: constants.CleanTypeUIAgentComponent,
				},
			)
			if _, err := l.svcCtx.ReporterProducer.Send(
				l.ctx, base.NewTask(
					constants.MQTaskTypeReporterSetCleanedTask,
					payload,
					base.WithQueueOptions(constants.MQNameReporterMQC),
				), base.QueuePriorityDefault,
			); err != nil {
				l.Errorf(
					"failed to send task to mq, type: %s, payload: %s, error: %+v",
					constants.MQTaskTypeReporterSetCleanedTask, payload, err,
				)
				return
			}

			l.Infof(
				"send delete ui agent component records task to mq successfully, type: %s, payload: %s",
				constants.MQTaskTypeReporterSetCleanedTask, payload,
			)
		},
		func(pipe <-chan any, cancel func(error)) {
		},
	)

	if err = l.svcCtx.UiAgentComponentExecutionRecordModel.DeleteRecordByComponentID(
		l.ctx, nil, in.GetProjectId(), in.GetComponentId(),
	); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to delete ui agent component execution record, project_id: %s, component_id: %s, error: %+v",
			in.GetProjectId(), in.GetComponentId(), err,
		)
	}

	return &pb.DeleteUIAgentComponentRecordResp{}, nil
}
